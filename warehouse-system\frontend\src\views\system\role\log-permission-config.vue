<template>
  <div class="log-permission-config">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>日志管理权限配置</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleRefresh">刷新</el-button>
      </div>
      
      <!-- 角色模板选择 -->
      <el-row :gutter="20" class="role-templates">
        <el-col :span="24">
          <h4>预设角色模板</h4>
          <el-row :gutter="10">
            <el-col :span="4" v-for="template in roleTemplates" :key="template.roleKey">
              <el-card 
                :class="['role-template-card', { 'selected': selectedTemplate === template.roleKey }]"
                @click.native="selectTemplate(template)"
              >
                <div class="template-icon">
                  <i :class="template.icon"></i>
                </div>
                <div class="template-name">{{ template.roleName }}</div>
                <div class="template-desc">{{ template.description }}</div>
                <div class="template-permissions">{{ template.permissionCount }}个权限</div>
              </el-card>
            </el-col>
          </row>
        </el-col>
      </el-row>

      <!-- 权限详情 -->
      <el-row :gutter="20" class="permission-details" v-if="selectedTemplate">
        <el-col :span="12">
          <h4>权限详情</h4>
          <el-tree
            :data="permissionTree"
            :props="treeProps"
            show-checkbox
            node-key="id"
            ref="permissionTree"
            :default-checked-keys="checkedPermissions"
            @check="handlePermissionCheck"
          />
        </el-col>
        <el-col :span="12">
          <h4>数据权限范围</h4>
          <el-radio-group v-model="dataScope" @change="handleDataScopeChange">
            <el-radio label="1">全部数据权限</el-radio>
            <el-radio label="2">自定数据权限</el-radio>
            <el-radio label="3">本部门数据权限</el-radio>
            <el-radio label="4">本部门及以下数据权限</el-radio>
            <el-radio label="5">仅本人数据权限</el-radio>
          </el-radio-group>
          
          <div v-if="dataScope === '2'" class="custom-data-scope">
            <h5>选择部门</h5>
            <el-tree
              :data="deptTree"
              :props="deptTreeProps"
              show-checkbox
              node-key="id"
              ref="deptTree"
              :default-checked-keys="checkedDepts"
              @check="handleDeptCheck"
            />
          </div>
        </el-col>
      </el-row>

      <!-- 用户分配 -->
      <el-row :gutter="20" class="user-assignment" v-if="selectedTemplate">
        <el-col :span="24">
          <h4>用户分配</h4>
          <el-transfer
            v-model="assignedUsers"
            :data="allUsers"
            :titles="['可分配用户', '已分配用户']"
            :button-texts="['移除', '分配']"
            :format="{
              noChecked: '${total}',
              hasChecked: '${checked}/${total}'
            }"
            @change="handleUserAssignment"
          />
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="selectedTemplate">
        <el-button type="primary" @click="handleSave">保存配置</el-button>
        <el-button @click="handleReset">重置</el-button>
        <el-button type="success" @click="handleApplyTemplate">应用模板</el-button>
      </div>
    </el-card>

    <!-- 权限预览对话框 -->
    <el-dialog title="权限预览" :visible.sync="previewDialogVisible" width="60%">
      <el-table :data="permissionPreview" border>
        <el-table-column prop="menuName" label="菜单名称" width="200" />
        <el-table-column prop="perms" label="权限标识" width="200" />
        <el-table-column prop="permissionType" label="权限类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getPermissionTypeColor(scope.row.permissionType)">
              {{ getPermissionTypeName(scope.row.permissionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'LogPermissionConfig',
  data() {
    return {
      selectedTemplate: '',
      dataScope: '1',
      assignedUsers: [],
      checkedPermissions: [],
      checkedDepts: [],
      previewDialogVisible: false,
      roleTemplates: [
        {
          roleKey: 'log_admin',
          roleName: '日志管理员',
          description: '拥有所有日志管理权限',
          icon: 'el-icon-user-solid',
          permissionCount: 20
        },
        {
          roleKey: 'log_viewer',
          roleName: '日志查看员',
          description: '只能查看和导出日志',
          icon: 'el-icon-view',
          permissionCount: 12
        },
        {
          roleKey: 'system_log_admin',
          roleName: '系统日志管理员',
          description: '管理系统操作日志',
          icon: 'el-icon-setting',
          permissionCount: 6
        },
        {
          roleKey: 'security_log_admin',
          roleName: '安全日志管理员',
          description: '管理登录和安全日志',
          icon: 'el-icon-lock',
          permissionCount: 6
        },
        {
          roleKey: 'stock_log_admin',
          roleName: '出入库日志管理员',
          description: '管理库存变动日志',
          icon: 'el-icon-goods',
          permissionCount: 5
        },
        {
          roleKey: 'error_log_admin',
          roleName: '错误日志管理员',
          description: '管理系统错误日志',
          icon: 'el-icon-warning',
          permissionCount: 6
        }
      ],
      permissionTree: [],
      deptTree: [],
      allUsers: [],
      permissionPreview: [],
      treeProps: {
        children: 'children',
        label: 'label'
      },
      deptTreeProps: {
        children: 'children',
        label: 'deptName'
      }
    }
  },
  mounted() {
    this.initData();
  },
  methods: {
    async initData() {
      await Promise.all([
        this.loadPermissionTree(),
        this.loadDeptTree(),
        this.loadAllUsers()
      ]);
    },

    selectTemplate(template) {
      this.selectedTemplate = template.roleKey;
      this.loadTemplateConfig(template.roleKey);
    },

    async loadTemplateConfig(roleKey) {
      // 加载角色的权限配置
      try {
        // 这里应该调用API获取角色的具体权限配置
        // const response = await getRolePermissions(roleKey);
        // this.checkedPermissions = response.permissions;
        // this.dataScope = response.dataScope;
        // this.assignedUsers = response.users;
        
        // 模拟数据
        this.checkedPermissions = this.getTemplatePermissions(roleKey);
        this.dataScope = this.getTemplateDataScope(roleKey);
        this.assignedUsers = [];
      } catch (error) {
        this.$message.error('加载角色配置失败');
      }
    },

    getTemplatePermissions(roleKey) {
      const permissionMap = {
        'log_admin': ['34', '35', '36', '3787', '4201', 'system_query', 'system_remove', 'system_export', 'system_clean'],
        'log_viewer': ['34', '35', '36', '3787', '4201', 'system_query', 'system_export'],
        'system_log_admin': ['34', '35', 'system_query', 'system_remove', 'system_export', 'system_clean'],
        'security_log_admin': ['34', '3787', 'security_query', 'security_remove', 'security_export', 'security_unlock'],
        'stock_log_admin': ['34', '36', 'stock_query', 'stock_remove', 'stock_export'],
        'error_log_admin': ['34', '4201', 'error_query', 'error_remove', 'error_export', 'error_clean']
      };
      return permissionMap[roleKey] || [];
    },

    getTemplateDataScope(roleKey) {
      const dataScopeMap = {
        'log_admin': '1',
        'log_viewer': '2',
        'system_log_admin': '1',
        'security_log_admin': '1',
        'stock_log_admin': '4',
        'error_log_admin': '1'
      };
      return dataScopeMap[roleKey] || '1';
    },

    async loadPermissionTree() {
      // 构建权限树结构
      this.permissionTree = [
        {
          id: '34',
          label: '日志管理',
          children: [
            {
              id: '35',
              label: '系统日志',
              children: [
                { id: 'system_query', label: '查询' },
                { id: 'system_remove', label: '删除' },
                { id: 'system_export', label: '导出' },
                { id: 'system_clean', label: '清空' }
              ]
            },
            {
              id: '36',
              label: '出入库日志',
              children: [
                { id: 'stock_query', label: '查询' },
                { id: 'stock_remove', label: '删除' },
                { id: 'stock_export', label: '导出' }
              ]
            },
            {
              id: '3787',
              label: '安全日志',
              children: [
                { id: 'security_query', label: '查询' },
                { id: 'security_remove', label: '删除' },
                { id: 'security_export', label: '导出' },
                { id: 'security_unlock', label: '用户解锁' }
              ]
            },
            {
              id: '4201',
              label: '错误日志',
              children: [
                { id: 'error_query', label: '查询' },
                { id: 'error_remove', label: '删除' },
                { id: 'error_export', label: '导出' },
                { id: 'error_clean', label: '清空' }
              ]
            }
          ]
        }
      ];
    },

    async loadDeptTree() {
      // 加载部门树
      this.deptTree = [
        {
          id: 1,
          deptName: '总公司',
          children: [
            { id: 2, deptName: '仓储部' },
            { id: 3, deptName: '系统管理部' },
            { id: 4, deptName: '安全管理部' }
          ]
        }
      ];
    },

    async loadAllUsers() {
      // 加载所有用户
      this.allUsers = [
        { key: 1, label: '管理员 (admin)' },
        { key: 2, label: '张三 (zhangsan)' },
        { key: 3, label: '李四 (lisi)' },
        { key: 4, label: '王五 (wangwu)' }
      ];
    },

    handlePermissionCheck(data, checked) {
      // 处理权限选择
      console.log('权限变更:', data, checked);
    },

    handleDataScopeChange(value) {
      console.log('数据权限范围变更:', value);
    },

    handleDeptCheck(data, checked) {
      // 处理部门选择
      console.log('部门变更:', data, checked);
    },

    handleUserAssignment(value, direction, movedKeys) {
      console.log('用户分配变更:', value, direction, movedKeys);
    },

    handleSave() {
      this.$message.success('权限配置保存成功');
    },

    handleReset() {
      this.loadTemplateConfig(this.selectedTemplate);
      this.$message.info('配置已重置');
    },

    handleApplyTemplate() {
      this.$confirm('确认应用此权限模板？这将覆盖现有配置。', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('权限模板应用成功');
      });
    },

    handleRefresh() {
      this.initData();
      this.$message.success('数据刷新成功');
    },

    getPermissionTypeColor(type) {
      const colorMap = {
        'READ': 'success',
        'WRITE': 'warning',
        'DELETE': 'danger',
        'OTHER': 'info'
      };
      return colorMap[type] || 'info';
    },

    getPermissionTypeName(type) {
      const nameMap = {
        'READ': '查看',
        'WRITE': '编辑',
        'DELETE': '删除',
        'OTHER': '其他'
      };
      return nameMap[type] || '未知';
    }
  }
}
</script>

<style scoped>
.log-permission-config {
  padding: 20px;
}

.role-templates {
  margin-bottom: 30px;
}

.role-template-card {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
  margin-bottom: 10px;
}

.role-template-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.role-template-card.selected {
  border-color: #409EFF;
  box-shadow: 0 0 10px rgba(64, 158, 255, 0.3);
}

.template-icon {
  font-size: 24px;
  color: #409EFF;
  margin-bottom: 10px;
}

.template-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.template-desc {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.template-permissions {
  font-size: 12px;
  color: #999;
}

.permission-details, .user-assignment {
  margin-bottom: 30px;
}

.custom-data-scope {
  margin-top: 15px;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.action-buttons {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
