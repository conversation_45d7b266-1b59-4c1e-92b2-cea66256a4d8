/**
 * 性能优化和用户体验提升样式
 * 包含加载动画、过渡效果、响应式优化等
 */

/* ================================
   全局加载状态样式
   ================================ */

.global-loading {
  cursor: wait;
  
  * {
    pointer-events: none;
  }
}

/* 加载遮罩层 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(2px);
  
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #409EFF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  .loading-text {
    margin-left: 15px;
    font-size: 14px;
    color: #666;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ================================
   骨架屏加载效果
   ================================ */

.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.skeleton-text {
  height: 16px;
  margin-bottom: 8px;
  border-radius: 4px;
  
  &.short {
    width: 60%;
  }
  
  &.medium {
    width: 80%;
  }
  
  &.long {
    width: 100%;
  }
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-button {
  width: 80px;
  height: 32px;
  border-radius: 4px;
}

/* ================================
   平滑过渡动画
   ================================ */

.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter, .fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter, .slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

/* 列表项动画 */
.list-enter-active, .list-leave-active {
  transition: all 0.3s ease;
}

.list-enter, .list-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

.list-move {
  transition: transform 0.3s ease;
}

/* ================================
   响应式优化
   ================================ */

/* 移动端优化 */
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }
  
  .el-table {
    font-size: 12px;
    
    .el-table__header th {
      padding: 8px 0;
    }
    
    .el-table__body td {
      padding: 8px 0;
    }
  }
  
  .el-form--inline .el-form-item {
    display: block;
    margin-bottom: 15px;
  }
  
  .el-button-group .el-button {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* 平板优化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .el-col-xl-6 {
    width: 50%;
  }
  
  .el-col-xl-4 {
    width: 33.33%;
  }
}

/* ================================
   性能优化样式
   ================================ */

/* 硬件加速 */
.hardware-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 减少重绘 */
.no-repaint {
  will-change: transform, opacity;
}

/* 图片懒加载占位 */
.lazy-image {
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  
  &.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }
  
  &.error {
    background: #fef0f0;
    color: #f56c6c;
  }
}

/* ================================
   用户体验增强
   ================================ */

/* 按钮点击反馈 */
.el-button {
  transition: all 0.2s ease;
  
  &:active {
    transform: translateY(1px);
  }
  
  &.loading {
    pointer-events: none;
    opacity: 0.7;
  }
}

/* 表格行悬停效果增强 */
.el-table__row {
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: #f5f7fa !important;
    transform: translateZ(0);
  }
}

/* 卡片悬停效果 */
.el-card {
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
  }
}

/* 输入框聚焦效果增强 */
.el-input__inner {
  transition: all 0.2s ease;
  
  &:focus {
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }
}

/* ================================
   错误状态样式
   ================================ */

.error-boundary {
  padding: 40px;
  text-align: center;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  margin: 20px 0;
  
  .error-icon {
    font-size: 48px;
    color: #f56c6c;
    margin-bottom: 16px;
  }
  
  .error-title {
    font-size: 18px;
    font-weight: 600;
    color: #f56c6c;
    margin-bottom: 8px;
  }
  
  .error-message {
    color: #999;
    margin-bottom: 16px;
  }
  
  .error-actions {
    .el-button {
      margin: 0 8px;
    }
  }
}

/* 网络状态指示器 */
.network-status {
  position: fixed;
  top: 60px;
  right: 20px;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  transition: all 0.3s ease;
  
  &.online {
    background: #f0f9ff;
    color: #67c23a;
    border: 1px solid #b3e19d;
  }
  
  &.offline {
    background: #fef0f0;
    color: #f56c6c;
    border: 1px solid #fbc4c4;
  }
}

/* ================================
   无障碍访问优化
   ================================ */

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .el-button--primary {
    background-color: #000;
    border-color: #000;
  }
  
  .el-table__row:hover {
    background-color: #000 !important;
    color: #fff;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 焦点可见性增强 */
.el-button:focus,
.el-input__inner:focus,
.el-select__input:focus {
  outline: 2px solid #409EFF;
  outline-offset: 2px;
}

/* ================================
   打印样式优化
   ================================ */

@media print {
  .no-print {
    display: none !important;
  }
  
  .el-table {
    border-collapse: collapse;
    
    th, td {
      border: 1px solid #ddd;
      padding: 8px;
    }
  }
  
  .el-card {
    box-shadow: none;
    border: 1px solid #ddd;
  }
}
