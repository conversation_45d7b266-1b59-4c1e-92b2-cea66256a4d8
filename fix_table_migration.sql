-- =============================================
-- Fix Table Migration Script
-- Complete the remaining data migration for standardized tables
-- =============================================

-- Set character set
SET NAMES utf8mb4;

-- Start transaction
START TRANSACTION;

-- =============================================
-- Step 1: Check current table status
-- =============================================
SELECT '=== Check current table status ===' as info;

-- Check if sys_stock_log exists and has data
SELECT 'sys_stock_log' as table_name, COUNT(*) as record_count FROM sys_stock_log;
SELECT 'wms_inventory_log' as table_name, COUNT(*) as record_count FROM wms_inventory_log;

-- =============================================
-- Step 2: Complete sys_stock_log migration if needed
-- =============================================
SELECT '=== Complete sys_stock_log migration ===' as info;

-- Migrate wms_inventory_log data to sys_stock_log if not already done
INSERT IGNORE INTO sys_stock_log (
    operation_type, warehouse_id, warehouse_name, product_id, product_name,
    product_code, product_spec, product_unit, quantity, before_quantity,
    after_quantity, unit_price, total_amount, related_order_id, related_order_type,
    operator, operator_id, operation_time, location_code, location_name,
    batch_no, expire_date, supplier_id, supplier_name, customer_id,
    customer_name, reason, remark, status, create_by, create_time,
    update_by, update_time
)
SELECT 
    operation_type, warehouse_id, warehouse_name, product_id, product_name,
    product_code, product_spec, product_unit, quantity, before_quantity,
    after_quantity, unit_price, total_amount, related_order_id, related_order_type,
    operator, operator_id, operation_time, location_code, location_name,
    batch_no, expire_date, supplier_id, supplier_name, customer_id,
    customer_name, reason, remark, status, create_by, create_time,
    update_by, update_time
FROM wms_inventory_log;

-- =============================================
-- Step 3: Create table aliases for backward compatibility
-- =============================================
SELECT '=== Create table aliases for backward compatibility ===' as info;

-- Create views to maintain backward compatibility
CREATE OR REPLACE VIEW v_sys_oper_log AS 
SELECT 
    log_id as oper_id,
    module as title,
    CASE 
        WHEN operation = 'INSERT' THEN 1
        WHEN operation = 'UPDATE' THEN 2
        WHEN operation = 'DELETE' THEN 3
        WHEN operation = 'GRANT' THEN 4
        WHEN operation = 'EXPORT' THEN 5
        WHEN operation = 'IMPORT' THEN 6
        WHEN operation = 'FORCE' THEN 7
        WHEN operation = 'GENCODE' THEN 8
        WHEN operation = 'CLEAN' THEN 9
        ELSE 0
    END as business_type,
    method,
    'GET' as request_method,
    0 as operator_type,
    user_name as oper_name,
    '' as dept_name,
    request_url as oper_url,
    request_ip as oper_ip,
    request_location as oper_location,
    request_param as oper_param,
    response_result as json_result,
    CAST(status AS UNSIGNED) as status,
    error_msg,
    cost_time,
    oper_time
FROM sys_system_log;

-- =============================================
-- Step 4: Verify final migration results
-- =============================================
SELECT '=== Verify final migration results ===' as info;

-- Check record counts for all standardized tables
SELECT 'sys_system_log' as table_name, COUNT(*) as record_count FROM sys_system_log
UNION ALL
SELECT 'sys_security_log' as table_name, COUNT(*) as record_count FROM sys_security_log
UNION ALL
SELECT 'sys_stock_log' as table_name, COUNT(*) as record_count FROM sys_stock_log
UNION ALL
SELECT 'sys_error_log' as table_name, COUNT(*) as record_count FROM sys_error_log;

-- Show table structure summary
SELECT '=== Standardized table summary ===' as info;
SELECT 
    TABLE_NAME as table_name,
    TABLE_COMMENT as table_comment,
    TABLE_ROWS as estimated_rows
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND TABLE_NAME IN ('sys_system_log', 'sys_security_log', 'sys_stock_log', 'sys_error_log')
ORDER BY TABLE_NAME;

-- Commit transaction
COMMIT;

SELECT '=== Table migration fix completed ===' as result;
SELECT 'All standardized tables are now ready: sys_system_log, sys_security_log, sys_stock_log, sys_error_log' as notice;
