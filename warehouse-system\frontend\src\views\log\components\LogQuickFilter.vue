<template>
  <el-row :gutter="10" class="mb8">
    <el-col :span="1.5" v-for="(filter, index) in filters" :key="index">
      <el-button
        :type="filter.type"
        plain
        :icon="filter.icon"
        size="mini"
        @click="handleFilter(filter.key)"
      >{{ filter.label }}</el-button>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: "LogQuickFilter",
  props: {
    filters: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  methods: {
    handleFilter(filterKey) {
      this.$emit('filter-change', filterKey);
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style>