-- =============================================
-- 日志管理系统菜单结构规范化脚本
-- 目标：整合冗余菜单，建立4个核心日志类型的统一结构
-- 执行前请备份数据库！
-- =============================================

-- 设置字符集
SET NAMES utf8mb4;

-- 开始事务
START TRANSACTION;

-- =============================================
-- 第一步：备份当前菜单结构
-- =============================================
SELECT '=== 备份当前日志菜单结构 ===' as info;
CREATE TABLE IF NOT EXISTS sys_menu_backup_log_optimization AS 
SELECT * FROM sys_menu WHERE menu_name LIKE '%日志%' OR parent_id = 34;

-- =============================================
-- 第二步：删除冗余和重复的菜单项
-- =============================================
SELECT '=== 删除冗余菜单项 ===' as info;

-- 删除重复的操作日志菜单及其子菜单权限
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE parent_id = 4413
);
DELETE FROM sys_menu WHERE parent_id = 4413;
DELETE FROM sys_role_menu WHERE menu_id = 4413;
DELETE FROM sys_menu WHERE menu_id = 4413;

-- 删除错误位置的分配日志菜单
DELETE FROM sys_role_menu WHERE menu_id = 4183;
DELETE FROM sys_menu WHERE menu_id = 4183;

-- 删除重复的登录日志菜单（保留安全日志）
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE parent_id = 37
);
DELETE FROM sys_menu WHERE parent_id = 37;
DELETE FROM sys_role_menu WHERE menu_id = 37;
DELETE FROM sys_menu WHERE menu_id = 37;

-- =============================================
-- 第三步：规范化现有菜单配置
-- =============================================
SELECT '=== 规范化菜单配置 ===' as info;

-- 确保日志管理父菜单存在且配置正确
UPDATE sys_menu SET 
    menu_name = '日志管理',
    path = 'log',
    component = 'Layout',
    order_num = 8,
    icon = 'log'
WHERE menu_id = 34;

-- 规范化系统日志菜单（整合操作日志功能）
UPDATE sys_menu SET 
    menu_name = '系统日志',
    path = 'system',
    component = 'log/system/index',
    perms = 'log:system:list',
    order_num = 1,
    icon = 'edit',
    remark = '系统日志菜单，整合操作和业务日志'
WHERE menu_id = 35;

-- 规范化出入库日志菜单
UPDATE sys_menu SET 
    menu_name = '出入库日志',
    path = 'stock',
    component = 'log/stock/index',
    perms = 'log:stock:list',
    order_num = 2,
    icon = 'shopping',
    remark = '出入库日志菜单'
WHERE menu_id = 36;

-- 规范化安全日志菜单（整合登录日志功能）
UPDATE sys_menu SET 
    menu_name = '安全日志',
    path = 'security',
    component = 'log/security/index',
    perms = 'log:security:list',
    order_num = 3,
    icon = 'lock',
    remark = '安全日志菜单，整合登录和权限日志'
WHERE menu_id = 3787;

-- 规范化错误日志菜单
UPDATE sys_menu SET 
    menu_name = '错误日志',
    path = 'error',
    component = 'log/error/index',
    perms = 'log:error:list',
    order_num = 4,
    icon = 'bug',
    remark = '错误日志菜单'
WHERE menu_id = 4201;

-- =============================================
-- 第四步：清理和规范化子菜单权限
-- =============================================
SELECT '=== 规范化子菜单权限 ===' as info;

-- 删除所有现有的日志子菜单权限
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu 
    WHERE parent_id IN (35, 36, 3787, 4201) AND menu_type = 'F'
);
DELETE FROM sys_menu WHERE parent_id IN (35, 36, 3787, 4201) AND menu_type = 'F';

-- 为系统日志添加标准权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('系统日志查询', 35, 1, 'log:system:query', 'F', '0', '0', 'admin', NOW()),
('系统日志删除', 35, 2, 'log:system:remove', 'F', '0', '0', 'admin', NOW()),
('系统日志导出', 35, 3, 'log:system:export', 'F', '0', '0', 'admin', NOW()),
('系统日志清空', 35, 4, 'log:system:clean', 'F', '0', '0', 'admin', NOW());

-- 为出入库日志添加标准权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('出入库日志查询', 36, 1, 'log:stock:query', 'F', '0', '0', 'admin', NOW()),
('出入库日志删除', 36, 2, 'log:stock:remove', 'F', '0', '0', 'admin', NOW()),
('出入库日志导出', 36, 3, 'log:stock:export', 'F', '0', '0', 'admin', NOW());

-- 为安全日志添加标准权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('安全日志查询', 3787, 1, 'log:security:query', 'F', '0', '0', 'admin', NOW()),
('安全日志删除', 3787, 2, 'log:security:remove', 'F', '0', '0', 'admin', NOW()),
('安全日志导出', 3787, 3, 'log:security:export', 'F', '0', '0', 'admin', NOW()),
('用户解锁', 3787, 4, 'log:security:unlock', 'F', '0', '0', 'admin', NOW());

-- 为错误日志添加标准权限按钮
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('错误日志查询', 4201, 1, 'log:error:query', 'F', '0', '0', 'admin', NOW()),
('错误日志删除', 4201, 2, 'log:error:remove', 'F', '0', '0', 'admin', NOW()),
('错误日志导出', 4201, 3, 'log:error:export', 'F', '0', '0', 'admin', NOW()),
('错误日志清空', 4201, 4, 'log:error:clean', 'F', '0', '0', 'admin', NOW());

-- =============================================
-- 第五步：为管理员角色分配新的权限
-- =============================================
SELECT '=== 为管理员分配权限 ===' as info;

-- 为超级管理员角色分配所有日志权限
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu 
WHERE parent_id = 34 OR menu_id = 34 OR parent_id IN (35, 36, 3787, 4201);

-- =============================================
-- 第六步：验证规范化结果
-- =============================================
SELECT '=== 验证规范化结果 ===' as info;

-- 显示最终的菜单结构
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    p.menu_name as parent_name,
    m.path,
    m.component,
    m.perms,
    m.order_num,
    m.menu_type
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_id = 34 OR m.parent_id = 34 OR m.parent_id IN (35, 36, 3787, 4201)
ORDER BY m.parent_id, m.order_num;

-- 统计权限数量
SELECT 
    '权限统计' as info,
    COUNT(*) as total_permissions
FROM sys_menu 
WHERE parent_id IN (35, 36, 3787, 4201) AND menu_type = 'F';

-- 提交事务
COMMIT;

SELECT '=== 日志菜单结构规范化完成 ===' as result;
SELECT '请重启后端服务以加载新的菜单配置' as notice;
