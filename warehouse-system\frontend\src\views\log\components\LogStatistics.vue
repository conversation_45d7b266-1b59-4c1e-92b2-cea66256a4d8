<template>
  <el-row :gutter="20" class="mb20">
    <el-col :span="colSpan" v-for="(stat, index) in statistics" :key="index">
      <el-card class="statistics-card">
        <div class="stat-item">
          <div class="stat-icon" :class="stat.iconClass">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: "LogStatistics",
  props: {
    statistics: {
      type: Array,
      required: true,
      default: () => []
    }
  },
  computed: {
    colSpan() {
      const count = this.statistics.length;
      if (count <= 4) return 24 / count;
      return 6; // 默认4列
    }
  }
};
</script>

<style scoped>
.mb20 {
  margin-bottom: 20px;
}

.statistics-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  align-items: center;
  padding: 20px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 24px;
  color: white;
}

.stat-icon.inventory-total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.inventory-today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.inventory-in {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.inventory-out {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon.error-total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.error-today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.error-unhandled {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.error-critical {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}
</style>