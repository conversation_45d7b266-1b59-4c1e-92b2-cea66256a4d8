-- =============================================
-- Data Integrity and Consistency Validation Script
-- Target: Ensure data integrity during migration process, establish data consistency validation mechanism
-- Please backup database before execution!
-- =============================================

-- Set character set
SET NAMES utf8mb4;

-- Start transaction
START TRANSACTION;

-- =============================================
-- Step 1: Create data validation functions
-- =============================================
SELECT '=== Create data validation functions ===' as info;

-- Create validation results table
CREATE TABLE IF NOT EXISTS sys_data_validation_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    validation_type VARCHAR(50) NOT NULL COMMENT '验证类型',
    table_name VARCHAR(100) NOT NULL COMMENT '表名',
    validation_rule VARCHAR(200) NOT NULL COMMENT '验证规则',
    expected_count BIGINT DEFAULT 0 COMMENT '期望数量',
    actual_count BIGINT DEFAULT 0 COMMENT '实际数量',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PASS,FAIL,PENDING',
    error_message TEXT COMMENT '错误信息',
    validation_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '验证时间',
    created_by VARCHAR(50) DEFAULT 'system' COMMENT '创建者',
    INDEX idx_validation_type (validation_type),
    INDEX idx_table_name (table_name),
    INDEX idx_status (status),
    INDEX idx_validation_time (validation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据验证日志表';

-- =============================================
-- Step 2: Validate log table data integrity
-- =============================================
SELECT '=== Validate log table data integrity ===' as info;

-- Validate system log data migration
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, expected_count, actual_count, status)
SELECT 
    'DATA_MIGRATION' as validation_type,
    'sys_system_log' as table_name,
    'Compare record count with original sys_oper_log' as validation_rule,
    (SELECT COUNT(*) FROM sys_oper_log) as expected_count,
    (SELECT COUNT(*) FROM sys_system_log) as actual_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM sys_oper_log) = (SELECT COUNT(*) FROM sys_system_log) THEN 'PASS'
        ELSE 'FAIL'
    END as status;

-- Validate security log data migration
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, expected_count, actual_count, status)
SELECT 
    'DATA_MIGRATION' as validation_type,
    'sys_security_log' as table_name,
    'Compare record count with original sys_logininfor' as validation_rule,
    (SELECT COUNT(*) FROM sys_logininfor) as expected_count,
    (SELECT COUNT(*) FROM sys_security_log) as actual_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM sys_logininfor) = (SELECT COUNT(*) FROM sys_security_log) THEN 'PASS'
        ELSE 'FAIL'
    END as status;

-- Validate stock log data migration
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, expected_count, actual_count, status)
SELECT 
    'DATA_MIGRATION' as validation_type,
    'sys_stock_log' as table_name,
    'Compare record count with original wms_inventory_log' as validation_rule,
    (SELECT COUNT(*) FROM wms_inventory_log) as expected_count,
    (SELECT COUNT(*) FROM sys_stock_log) as actual_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM wms_inventory_log) = (SELECT COUNT(*) FROM sys_stock_log) THEN 'PASS'
        ELSE 'FAIL'
    END as status;

-- =============================================
-- Step 3: Validate data consistency
-- =============================================
SELECT '=== Validate data consistency ===' as info;

-- Check for NULL values in critical fields
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
SELECT 
    'DATA_CONSISTENCY' as validation_type,
    'sys_system_log' as table_name,
    'Check for NULL values in critical fields' as validation_rule,
    COUNT(*) as actual_count,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status,
    CASE WHEN COUNT(*) > 0 THEN CONCAT('Found ', COUNT(*), ' records with NULL user_name') ELSE NULL END as error_message
FROM sys_system_log 
WHERE user_name IS NULL OR user_name = '';

-- Check for invalid operation types in stock log
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
SELECT 
    'DATA_CONSISTENCY' as validation_type,
    'sys_stock_log' as table_name,
    'Check for invalid operation types' as validation_rule,
    COUNT(*) as actual_count,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status,
    CASE WHEN COUNT(*) > 0 THEN CONCAT('Found ', COUNT(*), ' records with invalid operation_type') ELSE NULL END as error_message
FROM sys_stock_log 
WHERE operation_type NOT IN ('IN', 'OUT', 'TRANSFER', 'ADJUST', 'CHECK');

-- Check for negative quantities in stock log
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
SELECT 
    'DATA_CONSISTENCY' as validation_type,
    'sys_stock_log' as table_name,
    'Check for negative quantities' as validation_rule,
    COUNT(*) as actual_count,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status,
    CASE WHEN COUNT(*) > 0 THEN CONCAT('Found ', COUNT(*), ' records with negative quantity') ELSE NULL END as error_message
FROM sys_stock_log 
WHERE quantity < 0;

-- =============================================
-- Step 4: Validate referential integrity
-- =============================================
SELECT '=== Validate referential integrity ===' as info;

-- Check menu-role relationships
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
SELECT 
    'REFERENTIAL_INTEGRITY' as validation_type,
    'sys_role_menu' as table_name,
    'Check for orphaned role-menu relationships' as validation_rule,
    COUNT(*) as actual_count,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status,
    CASE WHEN COUNT(*) > 0 THEN CONCAT('Found ', COUNT(*), ' orphaned role-menu relationships') ELSE NULL END as error_message
FROM sys_role_menu rm
LEFT JOIN sys_role r ON rm.role_id = r.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IS NULL OR m.menu_id IS NULL;

-- Check user-role relationships
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
SELECT 
    'REFERENTIAL_INTEGRITY' as validation_type,
    'sys_user_role' as table_name,
    'Check for orphaned user-role relationships' as validation_rule,
    COUNT(*) as actual_count,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status,
    CASE WHEN COUNT(*) > 0 THEN CONCAT('Found ', COUNT(*), ' orphaned user-role relationships') ELSE NULL END as error_message
FROM sys_user_role ur
LEFT JOIN sys_user u ON ur.user_id = u.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_id IS NULL OR r.role_id IS NULL;

-- =============================================
-- Step 5: Create data consistency monitoring views
-- =============================================
SELECT '=== Create data consistency monitoring views ===' as info;

-- Create view for log data summary
CREATE OR REPLACE VIEW v_log_data_summary AS
SELECT 
    'sys_system_log' as table_name,
    COUNT(*) as record_count,
    MIN(create_time) as earliest_record,
    MAX(create_time) as latest_record,
    COUNT(DISTINCT user_name) as unique_users
FROM sys_system_log
UNION ALL
SELECT 
    'sys_security_log' as table_name,
    COUNT(*) as record_count,
    MIN(create_time) as earliest_record,
    MAX(create_time) as latest_record,
    COUNT(DISTINCT user_name) as unique_users
FROM sys_security_log
UNION ALL
SELECT 
    'sys_stock_log' as table_name,
    COUNT(*) as record_count,
    MIN(create_time) as earliest_record,
    MAX(create_time) as latest_record,
    COUNT(DISTINCT operator) as unique_users
FROM sys_stock_log
UNION ALL
SELECT 
    'sys_error_log' as table_name,
    COUNT(*) as record_count,
    MIN(create_time) as earliest_record,
    MAX(create_time) as latest_record,
    COUNT(DISTINCT create_by) as unique_users
FROM sys_error_log;

-- Create view for permission consistency check
CREATE OR REPLACE VIEW v_permission_consistency AS
SELECT 
    r.role_name,
    COUNT(rm.menu_id) as assigned_permissions,
    COUNT(CASE WHEN m.status = '0' THEN 1 END) as active_permissions,
    COUNT(CASE WHEN m.status = '1' THEN 1 END) as inactive_permissions,
    COUNT(CASE WHEN m.menu_id IS NULL THEN 1 END) as missing_menus
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key LIKE '%log%'
GROUP BY r.role_id, r.role_name;

-- =============================================
-- Step 6: Create automated validation procedures
-- =============================================
SELECT '=== Create automated validation procedures ===' as info;

DELIMITER //

-- Procedure to run daily data validation
CREATE PROCEDURE IF NOT EXISTS sp_daily_data_validation()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE validation_count INT DEFAULT 0;
    DECLARE failed_count INT DEFAULT 0;
    
    -- Clear old validation logs (keep last 30 days)
    DELETE FROM sys_data_validation_log 
    WHERE validation_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- Run basic data consistency checks
    INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status)
    SELECT 
        'DAILY_CHECK' as validation_type,
        'sys_system_log' as table_name,
        'Daily record count check' as validation_rule,
        COUNT(*) as actual_count,
        'PASS' as status
    FROM sys_system_log 
    WHERE DATE(create_time) = CURDATE();
    
    -- Check for data anomalies
    INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
    SELECT 
        'DAILY_CHECK' as validation_type,
        'sys_stock_log' as table_name,
        'Check for unusual stock movements' as validation_rule,
        COUNT(*) as actual_count,
        CASE WHEN COUNT(*) > 1000 THEN 'FAIL' ELSE 'PASS' END as status,
        CASE WHEN COUNT(*) > 1000 THEN 'Unusual high number of stock movements detected' ELSE NULL END as error_message
    FROM sys_stock_log 
    WHERE DATE(operation_time) = CURDATE();
    
    -- Get validation summary
    SELECT COUNT(*) INTO validation_count FROM sys_data_validation_log WHERE DATE(validation_time) = CURDATE();
    SELECT COUNT(*) INTO failed_count FROM sys_data_validation_log WHERE DATE(validation_time) = CURDATE() AND status = 'FAIL';
    
    -- Log summary
    INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_param, json_result, status, oper_time)
    VALUES ('数据完整性验证', 0, 'sp_daily_data_validation', 'PROCEDURE', 1, 'system', '系统管理', '/system/validation/daily', 
            CONCAT('validation_count:', validation_count), 
            CONCAT('{"total":', validation_count, ',"failed":', failed_count, '}'), 
            CASE WHEN failed_count = 0 THEN 0 ELSE 1 END, NOW());
END //

DELIMITER ;

-- =============================================
-- Step 7: Generate validation report
-- =============================================
SELECT '=== Generate validation report ===' as info;

-- Display validation summary
SELECT 
    validation_type,
    COUNT(*) as total_validations,
    COUNT(CASE WHEN status = 'PASS' THEN 1 END) as passed,
    COUNT(CASE WHEN status = 'FAIL' THEN 1 END) as failed,
    COUNT(CASE WHEN status = 'PENDING' THEN 1 END) as pending
FROM sys_data_validation_log
GROUP BY validation_type
ORDER BY validation_type;

-- Display failed validations
SELECT 
    table_name,
    validation_rule,
    expected_count,
    actual_count,
    error_message,
    validation_time
FROM sys_data_validation_log
WHERE status = 'FAIL'
ORDER BY validation_time DESC;

-- Display data summary
SELECT * FROM v_log_data_summary;

-- Display permission consistency
SELECT * FROM v_permission_consistency;

-- Commit transaction
COMMIT;

SELECT '=== Data integrity and consistency validation completed ===' as result;
SELECT 'Created validation framework with automated monitoring and reporting' as summary;
