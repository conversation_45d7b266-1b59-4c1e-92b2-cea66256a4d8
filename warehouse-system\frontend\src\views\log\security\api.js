import request from '@/utils/request'

// 查询安全日志列表
export function listSecurityLog(query) {
  return request({
    url: '/monitor/logininfor/list',
    method: 'get',
    params: query
  })
}

// 获取安全日志详细信息
export function getSecurityLog(logId) {
  return request({
    url: '/monitor/logininfor/' + logId,
    method: 'get'
  })
}

// 删除安全日志
export function delSecurityLog(logIds) {
  return request({
    url: '/monitor/logininfor/' + logIds,
    method: 'delete'
  })
}

// 清空安全日志
export function cleanSecurityLog() {
  return request({
    url: '/monitor/logininfor/clean',
    method: 'delete'
  })
}

// 导出安全日志
export function exportSecurityLog(query) {
  return request({
    url: '/monitor/logininfor/export',
    method: 'get',
    params: query
  })
}

// 解锁用户登录
export function unlockUser(userName) {
  return request({
    url: '/monitor/logininfor/unlock/' + userName,
    method: 'delete'
  })
}

// 批量解锁用户
export function batchUnlockUsers(userNames) {
  return request({
    url: '/monitor/logininfor/batchUnlock',
    method: 'delete',
    data: userNames
  })
}

// 获取安全日志统计信息
export function getSecurityLogStats(query) {
  return request({
    url: '/monitor/logininfor/stats',
    method: 'get',
    params: query
  })
}

// 获取安全日志趋势数据
export function getSecurityLogTrend(query) {
  return request({
    url: '/monitor/logininfor/trend',
    method: 'get',
    params: query
  })
}
