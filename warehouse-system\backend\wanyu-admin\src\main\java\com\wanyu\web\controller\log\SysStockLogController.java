package com.wanyu.web.controller.log;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.SysStockLog;
import com.wanyu.system.service.ISysStockLogService;

/**
 * 出入库日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@RequestMapping("/log/stock")
public class SysStockLogController extends BaseController
{
    @Autowired
    private ISysStockLogService stockLogService;

    /**
     * 查询出入库日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysStockLog stockLog)
    {
        startPage();
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        return getDataTable(list);
    }

    /**
     * 获取出入库日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:stock:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable Long logId)
    {
        return success(stockLogService.selectSysStockLogById(logId));
    }

    /**
     * 删除出入库日志
     */
    @PreAuthorize("@ss.hasPermi('log:stock:remove')")
    @Log(title = "出入库日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(stockLogService.deleteSysStockLogByIds(logIds));
    }

    /**
     * 导出出入库日志
     */
    @PreAuthorize("@ss.hasPermi('log:stock:export')")
    @Log(title = "出入库日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        ExcelUtil<SysStockLog> util = new ExcelUtil<SysStockLog>(SysStockLog.class);
        util.exportExcel(response, list, "出入库日志数据");
    }

    /**
     * 获取出入库日志统计信息
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/stats")
    public AjaxResult getStats(SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("total", list.size());
        
        // 按操作类型分类统计
        Map<String, Long> operationTypeStats = list.stream()
            .collect(Collectors.groupingBy(
                log -> log.getOperationType() != null ? log.getOperationType() : "UNKNOWN",
                Collectors.counting()
            ));
        stats.put("operationTypeStats", operationTypeStats);
        
        // 计算金额统计
        BigDecimal totalInAmount = list.stream()
            .filter(log -> "IN".equals(log.getOperationType()))
            .map(log -> log.getTotalAmount() != null ? log.getTotalAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        BigDecimal totalOutAmount = list.stream()
            .filter(log -> "OUT".equals(log.getOperationType()))
            .map(log -> log.getTotalAmount() != null ? log.getTotalAmount() : BigDecimal.ZERO)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        
        stats.put("totalInAmount", totalInAmount);
        stats.put("totalOutAmount", totalOutAmount);
        stats.put("netAmount", totalInAmount.subtract(totalOutAmount));
        
        return success(stats);
    }

    /**
     * 获取出入库日志趋势数据
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/trend")
    public AjaxResult getTrend(SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        
        // 按日期分组统计
        Map<String, Long> trendData = list.stream()
            .collect(Collectors.groupingBy(
                log -> log.getOperationTime().toString().substring(0, 10),
                Collectors.counting()
            ));
        
        return success(trendData);
    }

    /**
     * 获取库存变动分析
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/changeAnalysis")
    public AjaxResult getStockChangeAnalysis(SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        
        Map<String, Object> analysis = new HashMap<>();
        
        // 按商品分组分析
        Map<String, List<SysStockLog>> productGroups = list.stream()
            .collect(Collectors.groupingBy(
                log -> log.getProductName() != null ? log.getProductName() : "未知商品"
            ));
        
        Map<String, Object> productAnalysis = new HashMap<>();
        productGroups.forEach((productName, logs) -> {
            BigDecimal totalChange = logs.stream()
                .map(log -> {
                    BigDecimal quantity = log.getQuantity() != null ? log.getQuantity() : BigDecimal.ZERO;
                    return "OUT".equals(log.getOperationType()) ? quantity.negate() : quantity;
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            
            productAnalysis.put(productName, Map.of(
                "totalChange", totalChange,
                "operationCount", logs.size(),
                "lastOperation", logs.get(logs.size() - 1).getOperationTime()
            ));
        });
        
        analysis.put("productAnalysis", productAnalysis);
        
        return success(analysis);
    }

    /**
     * 获取仓库操作统计
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/warehouseStats")
    public AjaxResult getWarehouseOperationStats(SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        
        // 按仓库分组统计
        Map<String, Long> warehouseStats = list.stream()
            .collect(Collectors.groupingBy(
                log -> log.getWarehouseName() != null ? log.getWarehouseName() : "未知仓库",
                Collectors.counting()
            ));
        
        return success(warehouseStats);
    }

    /**
     * 获取商品操作统计
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/productStats")
    public AjaxResult getProductOperationStats(SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        
        // 按商品分组统计
        Map<String, Long> productStats = list.stream()
            .collect(Collectors.groupingBy(
                log -> log.getProductName() != null ? log.getProductName() : "未知商品",
                Collectors.counting()
            ));
        
        return success(productStats);
    }

    /**
     * 获取操作员统计
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/operatorStats")
    public AjaxResult getOperatorStats(SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        
        // 按操作员分组统计
        Map<String, Long> operatorStats = list.stream()
            .collect(Collectors.groupingBy(
                log -> log.getOperator() != null ? log.getOperator() : "未知操作员",
                Collectors.counting()
            ));
        
        return success(operatorStats);
    }

    /**
     * 获取库存健康度分析
     */
    @PreAuthorize("@ss.hasPermi('log:stock:list')")
    @GetMapping("/healthAnalysis")
    public AjaxResult getStockHealthAnalysis(SysStockLog stockLog)
    {
        List<SysStockLog> list = stockLogService.selectSysStockLogList(stockLog);
        
        Map<String, Object> healthAnalysis = new HashMap<>();
        
        // 分析库存为0的商品
        long zeroStockCount = list.stream()
            .filter(log -> "OUT".equals(log.getOperationType()))
            .filter(log -> log.getAfterQuantity() != null && log.getAfterQuantity().compareTo(BigDecimal.ZERO) == 0)
            .count();
        
        // 分析低库存商品（库存小于10）
        long lowStockCount = list.stream()
            .filter(log -> log.getAfterQuantity() != null && log.getAfterQuantity().compareTo(new BigDecimal("10")) < 0)
            .count();
        
        // 分析高风险操作（大额出库）
        long highRiskCount = list.stream()
            .filter(log -> "OUT".equals(log.getOperationType()))
            .filter(log -> log.getTotalAmount() != null && log.getTotalAmount().compareTo(new BigDecimal("10000")) >= 0)
            .count();
        
        healthAnalysis.put("zeroStockOperations", zeroStockCount);
        healthAnalysis.put("lowStockOperations", lowStockCount);
        healthAnalysis.put("highRiskOperations", highRiskCount);
        
        return success(healthAnalysis);
    }
}
