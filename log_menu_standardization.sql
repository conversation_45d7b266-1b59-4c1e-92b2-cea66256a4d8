-- =============================================
-- Log Management System Menu Structure Standardization Script
-- Target: Integrate redundant menus, establish 4 core log types unified structure
-- Please backup database before execution!
-- =============================================

-- Set character set
SET NAMES utf8mb4;

-- Start transaction
START TRANSACTION;

-- =============================================
-- Step 1: Backup current menu structure
-- =============================================
SELECT '=== Backup current log menu structure ===' as info;
CREATE TABLE IF NOT EXISTS sys_menu_backup_log_optimization AS 
SELECT * FROM sys_menu WHERE menu_name LIKE '%日志%' OR parent_id = 34;

-- =============================================
-- Step 2: Delete redundant and duplicate menu items
-- =============================================
SELECT '=== Delete redundant menu items ===' as info;

-- Delete duplicate operation log menu and its sub-menu permissions
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE parent_id = 4413
);
DELETE FROM sys_menu WHERE parent_id = 4413;
DELETE FROM sys_role_menu WHERE menu_id = 4413;
DELETE FROM sys_menu WHERE menu_id = 4413;

-- Delete misplaced allocation log menu
DELETE FROM sys_role_menu WHERE menu_id = 4183;
DELETE FROM sys_menu WHERE menu_id = 4183;

-- Delete duplicate login log menu (keep security log)
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu WHERE parent_id = 37
);
DELETE FROM sys_menu WHERE parent_id = 37;
DELETE FROM sys_role_menu WHERE menu_id = 37;
DELETE FROM sys_menu WHERE menu_id = 37;

-- =============================================
-- Step 3: Standardize existing menu configuration
-- =============================================
SELECT '=== Standardize menu configuration ===' as info;

-- Ensure log management parent menu exists and is configured correctly
UPDATE sys_menu SET 
    menu_name = '日志管理',
    path = 'log',
    component = 'Layout',
    order_num = 8,
    icon = 'log'
WHERE menu_id = 34;

-- Standardize system log menu (integrate operation log functionality)
UPDATE sys_menu SET 
    menu_name = '系统日志',
    path = 'system',
    component = 'log/system/index',
    perms = 'log:system:list',
    order_num = 1,
    icon = 'edit',
    remark = '系统日志菜单，整合操作和业务日志'
WHERE menu_id = 35;

-- Standardize inventory log menu
UPDATE sys_menu SET 
    menu_name = '出入库日志',
    path = 'stock',
    component = 'log/stock/index',
    perms = 'log:stock:list',
    order_num = 2,
    icon = 'shopping',
    remark = '出入库日志菜单'
WHERE menu_id = 36;

-- Standardize security log menu (integrate login log functionality)
UPDATE sys_menu SET 
    menu_name = '安全日志',
    path = 'security',
    component = 'log/security/index',
    perms = 'log:security:list',
    order_num = 3,
    icon = 'lock',
    remark = '安全日志菜单，整合登录和权限日志'
WHERE menu_id = 3787;

-- Standardize error log menu
UPDATE sys_menu SET 
    menu_name = '错误日志',
    path = 'error',
    component = 'log/error/index',
    perms = 'log:error:list',
    order_num = 4,
    icon = 'bug',
    remark = '错误日志菜单'
WHERE menu_id = 4201;

-- =============================================
-- Step 4: Clean and standardize sub-menu permissions
-- =============================================
SELECT '=== Standardize sub-menu permissions ===' as info;

-- Delete all existing log sub-menu permissions
DELETE FROM sys_role_menu WHERE menu_id IN (
    SELECT menu_id FROM sys_menu 
    WHERE parent_id IN (35, 36, 3787, 4201) AND menu_type = 'F'
);
DELETE FROM sys_menu WHERE parent_id IN (35, 36, 3787, 4201) AND menu_type = 'F';

-- Add standard permission buttons for system log
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('系统日志查询', 35, 1, 'log:system:query', 'F', '0', '0', 'admin', NOW()),
('系统日志删除', 35, 2, 'log:system:remove', 'F', '0', '0', 'admin', NOW()),
('系统日志导出', 35, 3, 'log:system:export', 'F', '0', '0', 'admin', NOW()),
('系统日志清空', 35, 4, 'log:system:clean', 'F', '0', '0', 'admin', NOW());

-- Add standard permission buttons for inventory log
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('出入库日志查询', 36, 1, 'log:stock:query', 'F', '0', '0', 'admin', NOW()),
('出入库日志删除', 36, 2, 'log:stock:remove', 'F', '0', '0', 'admin', NOW()),
('出入库日志导出', 36, 3, 'log:stock:export', 'F', '0', '0', 'admin', NOW());

-- Add standard permission buttons for security log
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('安全日志查询', 3787, 1, 'log:security:query', 'F', '0', '0', 'admin', NOW()),
('安全日志删除', 3787, 2, 'log:security:remove', 'F', '0', '0', 'admin', NOW()),
('安全日志导出', 3787, 3, 'log:security:export', 'F', '0', '0', 'admin', NOW()),
('用户解锁', 3787, 4, 'log:security:unlock', 'F', '0', '0', 'admin', NOW());

-- Add standard permission buttons for error log
INSERT INTO sys_menu (menu_name, parent_id, order_num, perms, menu_type, visible, status, create_by, create_time) VALUES
('错误日志查询', 4201, 1, 'log:error:query', 'F', '0', '0', 'admin', NOW()),
('错误日志删除', 4201, 2, 'log:error:remove', 'F', '0', '0', 'admin', NOW()),
('错误日志导出', 4201, 3, 'log:error:export', 'F', '0', '0', 'admin', NOW()),
('错误日志清空', 4201, 4, 'log:error:clean', 'F', '0', '0', 'admin', NOW());

-- =============================================
-- Step 5: Assign new permissions to admin roles
-- =============================================
SELECT '=== Assign permissions to admin ===' as info;

-- Assign all log permissions to super admin role
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT 1, menu_id FROM sys_menu 
WHERE parent_id = 34 OR menu_id = 34 OR parent_id IN (35, 36, 3787, 4201);

-- =============================================
-- Step 6: Verify standardization results
-- =============================================
SELECT '=== Verify standardization results ===' as info;

-- Display final menu structure
SELECT 
    m.menu_id,
    m.menu_name,
    m.parent_id,
    p.menu_name as parent_name,
    m.path,
    m.component,
    m.perms,
    m.order_num,
    m.menu_type
FROM sys_menu m
LEFT JOIN sys_menu p ON m.parent_id = p.menu_id
WHERE m.menu_id = 34 OR m.parent_id = 34 OR m.parent_id IN (35, 36, 3787, 4201)
ORDER BY m.parent_id, m.order_num;

-- Count permissions
SELECT 
    'Permission Statistics' as info,
    COUNT(*) as total_permissions
FROM sys_menu 
WHERE parent_id IN (35, 36, 3787, 4201) AND menu_type = 'F';

-- Commit transaction
COMMIT;

SELECT '=== Log menu structure standardization completed ===' as result;
SELECT 'Please restart backend service to load new menu configuration' as notice;
