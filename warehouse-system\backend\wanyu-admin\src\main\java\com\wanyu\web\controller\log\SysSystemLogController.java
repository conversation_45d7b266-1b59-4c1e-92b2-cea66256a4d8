package com.wanyu.web.controller.log;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wanyu.common.annotation.Log;
import com.wanyu.common.core.controller.BaseController;
import com.wanyu.common.core.domain.AjaxResult;
import com.wanyu.common.core.page.TableDataInfo;
import com.wanyu.common.enums.BusinessType;
import com.wanyu.common.utils.poi.ExcelUtil;
import com.wanyu.system.domain.SysOperLog;
import com.wanyu.system.service.ISysOperLogService;

/**
 * 系统日志Controller
 * 
 * <AUTHOR>
 * @date 2025-01-08
 */
@RestController
@RequestMapping("/log/system")
public class SysSystemLogController extends BaseController
{
    @Autowired
    private ISysOperLogService operLogService;

    /**
     * 查询系统日志列表
     */
    @PreAuthorize("@ss.hasPermi('log:system:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysOperLog operLog)
    {
        startPage();
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        return getDataTable(list);
    }

    /**
     * 获取系统日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('log:system:query')")
    @GetMapping(value = "/{operId}")
    public AjaxResult getInfo(@PathVariable Long operId)
    {
        return success(operLogService.selectOperLogById(operId));
    }

    /**
     * 删除系统日志
     */
    @PreAuthorize("@ss.hasPermi('log:system:remove')")
    @Log(title = "系统日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{operIds}")
    public AjaxResult remove(@PathVariable Long[] operIds)
    {
        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    /**
     * 清空系统日志
     */
    @PreAuthorize("@ss.hasPermi('log:system:clean')")
    @Log(title = "系统日志", businessType = BusinessType.CLEAN)
    @DeleteMapping("/clean")
    public AjaxResult clean()
    {
        operLogService.cleanOperLog();
        return success();
    }

    /**
     * 导出系统日志
     */
    @PreAuthorize("@ss.hasPermi('log:system:export')")
    @Log(title = "系统日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysOperLog operLog)
    {
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        ExcelUtil<SysOperLog> util = new ExcelUtil<SysOperLog>(SysOperLog.class);
        util.exportExcel(response, list, "系统日志数据");
    }

    /**
     * 获取系统日志统计信息
     */
    @PreAuthorize("@ss.hasPermi('log:system:list')")
    @GetMapping("/stats")
    public AjaxResult getStats(SysOperLog operLog)
    {
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("total", list.size());
        stats.put("success", list.stream().filter(log -> log.getStatus() == 0).count());
        stats.put("failed", list.stream().filter(log -> log.getStatus() == 1).count());
        
        // 按业务类型分类统计
        Map<String, Long> businessTypeStats = list.stream()
            .collect(Collectors.groupingBy(
                log -> getBusinessTypeName(log.getBusinessType()),
                Collectors.counting()
            ));
        stats.put("businessTypeStats", businessTypeStats);
        
        return success(stats);
    }

    /**
     * 获取系统日志趋势数据
     */
    @PreAuthorize("@ss.hasPermi('log:system:list')")
    @GetMapping("/trend")
    public AjaxResult getTrend(SysOperLog operLog)
    {
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
        
        // 按日期分组统计
        Map<String, Long> trendData = list.stream()
            .collect(Collectors.groupingBy(
                log -> log.getOperTime().toString().substring(0, 10),
                Collectors.counting()
            ));
        
        return success(trendData);
    }

    /**
     * 获取业务类型名称
     */
    private String getBusinessTypeName(Integer businessType) {
        if (businessType == null) return "其他";
        
        switch (businessType) {
            case 1: return "新增";
            case 2: return "修改";
            case 3: return "删除";
            case 4: return "授权";
            case 5: return "导出";
            case 6: return "导入";
            case 7: return "强退";
            case 8: return "生成代码";
            case 9: return "清空数据";
            default: return "其他";
        }
    }
}
