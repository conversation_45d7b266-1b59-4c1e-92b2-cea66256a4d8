import request from '@/utils/request'

// 查询系统日志列表
export function listSystemLog(query) {
  return request({
    url: '/monitor/operlog/list',
    method: 'get',
    params: query
  })
}

// 获取系统日志详细信息
export function getSystemLog(logId) {
  return request({
    url: '/monitor/operlog/' + logId,
    method: 'get'
  })
}

// 删除系统日志
export function delSystemLog(logIds) {
  return request({
    url: '/monitor/operlog/' + logIds,
    method: 'delete'
  })
}

// 清空系统日志
export function cleanSystemLog() {
  return request({
    url: '/monitor/operlog/clean',
    method: 'delete'
  })
}

// 导出系统日志
export function exportSystemLog(query) {
  return request({
    url: '/monitor/operlog/export',
    method: 'get',
    params: query
  })
}

// 获取系统日志统计信息
export function getSystemLogStats(query) {
  return request({
    url: '/monitor/operlog/stats',
    method: 'get',
    params: query
  })
}

// 获取系统日志趋势数据
export function getSystemLogTrend(query) {
  return request({
    url: '/monitor/operlog/trend',
    method: 'get',
    params: query
  })
}
