-- =============================================
-- Database Table Naming Standardization Script
-- Target: Unify all log-related database table naming conventions using sys_ prefix
-- Please backup database before execution!
-- =============================================

-- Set character set
SET NAMES utf8mb4;

-- Start transaction
START TRANSACTION;

-- =============================================
-- Step 1: Backup existing tables
-- =============================================
SELECT '=== Backup existing tables ===' as info;

-- Backup sys_logininfor table
CREATE TABLE IF NOT EXISTS sys_logininfor_backup_naming AS SELECT * FROM sys_logininfor;

-- Backup wms_inventory_log table
CREATE TABLE IF NOT EXISTS wms_inventory_log_backup_naming AS SELECT * FROM wms_inventory_log;

-- =============================================
-- Step 2: Create new standardized tables
-- =============================================
SELECT '=== Create standardized tables ===' as info;

-- Create sys_system_log table (rename from sys_oper_log)
CREATE TABLE IF NOT EXISTS sys_system_log (
    log_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    user_name VARCHAR(50) DEFAULT '' COMMENT '操作用户',
    user_id BIGINT DEFAULT NULL COMMENT '用户ID',
    module VARCHAR(50) DEFAULT '' COMMENT '操作模块',
    operation VARCHAR(50) DEFAULT '' COMMENT '操作类型',
    method VARCHAR(100) DEFAULT '' COMMENT '请求方法',
    request_url VARCHAR(255) DEFAULT '' COMMENT '请求URL',
    request_ip VARCHAR(128) DEFAULT '' COMMENT '请求IP',
    request_location VARCHAR(255) DEFAULT '' COMMENT '操作地点',
    request_param VARCHAR(2000) DEFAULT '' COMMENT '请求参数',
    response_result VARCHAR(2000) DEFAULT '' COMMENT '响应结果',
    log_type VARCHAR(20) DEFAULT 'SYSTEM' COMMENT '日志类型：SYSTEM,BUSINESS,DATA',
    status CHAR(1) DEFAULT '0' COMMENT '操作状态：0成功 1失败',
    error_msg VARCHAR(2000) DEFAULT '' COMMENT '错误信息',
    cost_time BIGINT DEFAULT 0 COMMENT '执行时间',
    oper_time DATETIME DEFAULT NULL COMMENT '操作时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (log_id),
    KEY idx_user_name (user_name),
    KEY idx_status (status),
    KEY idx_oper_time (oper_time),
    KEY idx_log_type (log_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- Create sys_security_log table (rename from sys_logininfor)
CREATE TABLE IF NOT EXISTS sys_security_log (
    log_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    user_name VARCHAR(50) DEFAULT '' COMMENT '用户名',
    user_id BIGINT DEFAULT NULL COMMENT '用户ID',
    login_ip VARCHAR(128) DEFAULT '' COMMENT '登录IP',
    login_location VARCHAR(255) DEFAULT '' COMMENT '登录地点',
    browser VARCHAR(50) DEFAULT '' COMMENT '浏览器',
    os VARCHAR(50) DEFAULT '' COMMENT '操作系统',
    event_type VARCHAR(20) DEFAULT 'LOGIN' COMMENT '事件类型：LOGIN,LOGOUT,PERMISSION',
    status CHAR(1) DEFAULT '0' COMMENT '登录状态：0成功 1失败',
    msg VARCHAR(255) DEFAULT '' COMMENT '提示信息',
    login_time DATETIME DEFAULT NULL COMMENT '登录时间',
    logout_time DATETIME DEFAULT NULL COMMENT '退出时间',
    session_id VARCHAR(100) DEFAULT '' COMMENT '会话ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (log_id),
    KEY idx_user_name (user_name),
    KEY idx_status (status),
    KEY idx_login_time (login_time),
    KEY idx_event_type (event_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='安全日志表';

-- Create sys_stock_log table (rename from wms_inventory_log)
CREATE TABLE IF NOT EXISTS sys_stock_log (
    log_id BIGINT NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    operation_type VARCHAR(20) NOT NULL DEFAULT 'IN' COMMENT '操作类型：IN入库,OUT出库,TRANSFER调拨,CHECK盘点',
    warehouse_id BIGINT NOT NULL COMMENT '仓库ID',
    warehouse_name VARCHAR(100) DEFAULT '' COMMENT '仓库名称',
    product_id BIGINT DEFAULT NULL COMMENT '商品ID',
    product_name VARCHAR(200) DEFAULT NULL COMMENT '商品名称',
    product_code VARCHAR(50) DEFAULT NULL COMMENT '商品编码',
    product_spec VARCHAR(200) DEFAULT NULL COMMENT '商品规格',
    product_unit VARCHAR(20) DEFAULT NULL COMMENT '商品单位',
    quantity DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作数量',
    before_quantity DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作前数量',
    after_quantity DECIMAL(10,2) NOT NULL DEFAULT 0.00 COMMENT '操作后数量',
    unit_price DECIMAL(10,2) DEFAULT 0.00 COMMENT '单价',
    total_amount DECIMAL(12,2) DEFAULT 0.00 COMMENT '总金额',
    related_order_id VARCHAR(100) DEFAULT '' COMMENT '关联单据ID',
    related_order_type VARCHAR(20) DEFAULT NULL COMMENT '关联单据类型',
    operator VARCHAR(50) NOT NULL DEFAULT 'system' COMMENT '操作人员',
    operator_id BIGINT DEFAULT NULL COMMENT '操作人员ID',
    operation_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    location_code VARCHAR(50) DEFAULT NULL COMMENT '库位编码',
    location_name VARCHAR(100) DEFAULT NULL COMMENT '库位名称',
    batch_no VARCHAR(50) DEFAULT NULL COMMENT '批次号',
    expire_date DATE DEFAULT NULL COMMENT '过期日期',
    supplier_id BIGINT DEFAULT NULL COMMENT '供应商ID',
    supplier_name VARCHAR(100) DEFAULT NULL COMMENT '供应商名称',
    customer_id BIGINT DEFAULT NULL COMMENT '客户ID',
    customer_name VARCHAR(100) DEFAULT NULL COMMENT '客户名称',
    reason VARCHAR(200) DEFAULT NULL COMMENT '操作原因',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    status CHAR(1) DEFAULT '0' COMMENT '状态：0正常 1异常',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (log_id),
    KEY idx_operation_type (operation_type),
    KEY idx_warehouse_id (warehouse_id),
    KEY idx_product_id (product_id),
    KEY idx_related_order_id (related_order_id),
    KEY idx_operator (operator),
    KEY idx_operation_time (operation_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出入库日志表';

-- =============================================
-- Step 3: Migrate data to new tables
-- =============================================
SELECT '=== Migrate data to new tables ===' as info;

-- Migrate sys_oper_log data to sys_system_log
INSERT INTO sys_system_log (
    user_name, module, operation, method, request_url, request_ip, 
    request_location, request_param, response_result, 
    log_type, status, error_msg, cost_time, oper_time, create_time
)
SELECT 
    oper_name as user_name,
    title as module,
    CASE 
        WHEN business_type = 1 THEN 'INSERT'
        WHEN business_type = 2 THEN 'UPDATE'
        WHEN business_type = 3 THEN 'DELETE'
        WHEN business_type = 4 THEN 'GRANT'
        WHEN business_type = 5 THEN 'EXPORT'
        WHEN business_type = 6 THEN 'IMPORT'
        WHEN business_type = 7 THEN 'FORCE'
        WHEN business_type = 8 THEN 'GENCODE'
        WHEN business_type = 9 THEN 'CLEAN'
        ELSE 'OTHER'
    END as operation,
    method,
    oper_url as request_url,
    oper_ip as request_ip,
    oper_location as request_location,
    oper_param as request_param,
    json_result as response_result,
    CASE 
        WHEN business_type IN (0, 4, 7, 8, 9) THEN 'SYSTEM'
        WHEN business_type IN (1, 2, 3) THEN 'BUSINESS'
        WHEN business_type IN (5, 6) THEN 'DATA'
        ELSE 'SYSTEM'
    END as log_type,
    CAST(status AS CHAR(1)) as status,
    error_msg,
    cost_time,
    oper_time,
    NOW() as create_time
FROM sys_oper_log;

-- Migrate sys_logininfor data to sys_security_log
INSERT INTO sys_security_log (
    user_name, login_ip, login_location, browser, os, 
    event_type, status, msg, login_time, create_time
)
SELECT 
    user_name,
    ipaddr as login_ip,
    login_location,
    browser,
    os,
    'LOGIN' as event_type,
    status,
    msg,
    login_time,
    NOW() as create_time
FROM sys_logininfor;

-- Migrate wms_inventory_log data to sys_stock_log
INSERT INTO sys_stock_log (
    operation_type, warehouse_id, warehouse_name, product_id, product_name,
    product_code, product_spec, product_unit, quantity, before_quantity,
    after_quantity, unit_price, total_amount, related_order_id, related_order_type,
    operator, operator_id, operation_time, location_code, location_name,
    batch_no, expire_date, supplier_id, supplier_name, customer_id,
    customer_name, reason, remark, status, create_by, create_time,
    update_by, update_time
)
SELECT 
    operation_type, warehouse_id, warehouse_name, product_id, product_name,
    product_code, product_spec, product_unit, quantity, before_quantity,
    after_quantity, unit_price, total_amount, related_order_id, related_order_type,
    operator, operator_id, operation_time, location_code, location_name,
    batch_no, expire_date, supplier_id, supplier_name, customer_id,
    customer_name, reason, remark, status, create_by, create_time,
    update_by, update_time
FROM wms_inventory_log;

-- =============================================
-- Step 4: Verify data migration
-- =============================================
SELECT '=== Verify data migration ===' as info;

-- Check record counts
SELECT 'sys_oper_log' as source_table, COUNT(*) as record_count FROM sys_oper_log
UNION ALL
SELECT 'sys_system_log' as source_table, COUNT(*) as record_count FROM sys_system_log
UNION ALL
SELECT 'sys_logininfor' as source_table, COUNT(*) as record_count FROM sys_logininfor
UNION ALL
SELECT 'sys_security_log' as source_table, COUNT(*) as record_count FROM sys_security_log
UNION ALL
SELECT 'wms_inventory_log' as source_table, COUNT(*) as record_count FROM wms_inventory_log
UNION ALL
SELECT 'sys_stock_log' as source_table, COUNT(*) as record_count FROM sys_stock_log;

-- Commit transaction
COMMIT;

SELECT '=== Database table naming standardization completed ===' as result;
SELECT 'New standardized tables created: sys_system_log, sys_security_log, sys_stock_log' as notice;
SELECT 'Original tables preserved as backup. Please update application configuration.' as warning;
