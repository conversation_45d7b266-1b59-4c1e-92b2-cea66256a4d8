<template>
  <div class="log-management-container">
    <!-- 统计信息 -->
    <LogStatistics :statistics="statisticsData" />
    
    <!-- 趋势图表 -->
    <LogTrendChart
      :visible="showTrend"
      :title="trendTitle"
      :trend-data="trendData"
      :period="trendPeriod"
      :chart-config="chartConfig"
      @period-change="handlePeriodChange"
    />
    
    <!-- 快速筛选 -->
    <LogQuickFilter
      :filters="quickFilters"
      @filter-change="handleQuickFilter"
    />
    
    <!-- 批量操作 -->
    <LogBatchActions
      :main-actions="mainActions"
      :batch-actions="batchActions"
      :extra-actions="extraActions"
      :show-search.sync="showSearchSync"
      @action="handleMainAction"
      @batch-action="handleBatchAction"
      @refresh="handleRefresh"
    />
    
    <!-- 插槽：自定义内容 -->
    <slot></slot>
  </div>
</template>

<script>
import LogStatistics from './LogStatistics.vue';
import LogTrendChart from './LogTrendChart.vue';
import LogQuickFilter from './LogQuickFilter.vue';
import LogBatchActions from './LogBatchActions.vue';

export default {
  name: "LogManagement",
  components: {
    LogStatistics,
    LogTrendChart,
    LogQuickFilter,
    LogBatchActions
  },
  props: {
    // 统计数据
    statisticsData: {
      type: Array,
      default: () => []
    },
    // 趋势图表相关
    showTrend: {
      type: Boolean,
      default: true
    },
    trendTitle: {
      type: String,
      default: '趋势分析'
    },
    trendData: {
      type: Array,
      default: () => []
    },
    trendPeriod: {
      type: String,
      default: '7d'
    },
    chartConfig: {
      type: Object,
      default: () => ({
        series: [],
        yAxisName: '数量'
      })
    },
    // 快速筛选
    quickFilters: {
      type: Array,
      default: () => []
    },
    // 批量操作
    mainActions: {
      type: Array,
      default: () => []
    },
    batchActions: {
      type: Array,
      default: () => []
    },
    extraActions: {
      type: Array,
      default: () => []
    },
    showSearch: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    showSearchSync: {
      get() {
        return this.showSearch
      },
      set(value) {
        this.$emit('update:showSearch', value)
      }
    }
  },
  methods: {
    handlePeriodChange(period) {
      this.$emit('period-change', period);
    },
    handleQuickFilter(filterKey) {
      this.$emit('quick-filter', filterKey);
    },
    handleMainAction(actionKey) {
      this.$emit('main-action', actionKey);
    },
    handleBatchAction(actionKey) {
      this.$emit('batch-action', actionKey);
    },
    handleRefresh() {
      this.$emit('refresh');
    }
  }
};
</script>

<style scoped>
.log-management-container {
  padding: 0;
}
</style>