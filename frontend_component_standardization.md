# 前端组件标准化执行计划

## 📋 当前状态分析

### 现有组件结构
```
frontend/src/
├── components/LogManagement/     # 现有通用组件位置
│   ├── index.vue
│   ├── LogStatistics.vue
│   ├── LogTrendChart.vue
│   ├── LogQuickFilter.vue
│   └── LogBatchActions.vue
├── views/log/                    # 日志页面目录
│   ├── system/index.vue         ✅ 已存在
│   ├── security/index.vue       ✅ 已存在
│   ├── error/index.vue          ✅ 已存在
│   └── stock/index.vue          ✅ 已存在
└── api/log/                     # API文件
    └── login.js                 ❌ 需要重新组织
```

## 🎯 目标结构

### 标准化后的目录结构
```
frontend/src/views/log/
├── system/
│   ├── index.vue               # 系统日志主页面
│   ├── components/             # 系统日志专用组件
│   └── api.js                  # 系统日志API接口
├── security/
│   ├── index.vue               # 安全日志主页面
│   ├── components/             # 安全日志专用组件
│   └── api.js                  # 安全日志API接口
├── error/
│   ├── index.vue               # 错误日志主页面
│   ├── components/             # 错误日志专用组件
│   └── api.js                  # 错误日志API接口
├── stock/
│   ├── index.vue               # 出入库日志主页面
│   ├── components/             # 出入库日志专用组件
│   └── api.js                  # 出入库日志API接口
└── components/                 # 通用日志组件
    ├── LogManagement.vue       # 统一的日志管理布局组件
    ├── LogSearch.vue           # 通用的日志搜索表单组件
    ├── LogTable.vue            # 通用的日志数据表格组件
    ├── LogStats.vue            # 通用的日志统计卡片组件
    └── index.js                # 组件导出文件
```

## 📝 执行步骤

### 第一步：创建通用组件目录
1. 创建 `views/log/components/` 目录
2. 移动现有的LogManagement相关组件到新目录
3. 重新组织组件结构

### 第二步：创建各日志类型的API文件
1. 创建 `views/log/system/api.js` - 系统日志API
2. 创建 `views/log/security/api.js` - 安全日志API  
3. 创建 `views/log/error/api.js` - 错误日志API
4. 创建 `views/log/stock/api.js` - 出入库日志API

### 第三步：创建专用组件目录
1. 为每个日志类型创建components子目录
2. 根据需要创建专用组件

### 第四步：更新组件引用
1. 更新各页面组件中的import路径
2. 确保所有引用都指向新的组件位置

### 第五步：清理旧文件
1. 删除 `src/components/LogManagement/` 目录
2. 重新组织 `src/api/log/` 目录结构

## ⚠️ 注意事项

1. **向后兼容性**：确保现有页面功能不受影响
2. **组件复用**：通用组件应该支持所有日志类型
3. **API统一性**：所有API接口应遵循统一的命名规范
4. **样式一致性**：保持所有日志页面的视觉风格统一

## 🔧 技术要求

1. **组件设计**：使用Vue 2.x语法，支持props和events
2. **API设计**：使用axios进行HTTP请求，统一错误处理
3. **样式规范**：使用Element UI组件库，遵循现有设计规范
4. **代码质量**：添加必要的注释和文档

---

**执行时间**：预计2-3小时  
**影响范围**：前端日志管理模块  
**风险等级**：中等（需要仔细测试组件引用）
