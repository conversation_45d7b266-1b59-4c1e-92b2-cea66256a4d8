-- =============================================
-- Data Integrity Fix Script
-- Target: Fix data integrity issues identified by validation
-- Please backup database before execution!
-- =============================================

-- Set character set
SET NAMES utf8mb4;

-- Start transaction
START TRANSACTION;

-- =============================================
-- Step 1: Fix data migration issues
-- =============================================
SELECT '=== Fix data migration issues ===' as info;

-- Fix sys_security_log migration - re-migrate data from sys_logininfor
TRUNCATE TABLE sys_security_log;

INSERT INTO sys_security_log (
    user_name, event_type, event_desc, risk_level, client_ip,
    client_location, user_agent, status, event_time, create_time
)
SELECT
    user_name,
    'LOGIN' as event_type,
    CASE
        WHEN status = '0' THEN '用户登录成功'
        ELSE '用户登录失败'
    END as event_desc,
    CASE
        WHEN status = '0' THEN 'LOW'
        ELSE 'HIGH'
    END as risk_level,
    ipaddr as client_ip,
    login_location as client_location,
    CONCAT(browser, ' / ', os) as user_agent,
    status,
    login_time as event_time,
    login_time as create_time
FROM sys_logininfor;

-- Verify the fix
SELECT CONCAT('Fixed sys_security_log: ', COUNT(*), ' records migrated') as fix_result
FROM sys_security_log;

-- =============================================
-- Step 2: Fix negative quantities in stock log
-- =============================================
SELECT '=== Fix negative quantities in stock log ===' as info;

-- Update negative quantities to absolute values and add a note
UPDATE sys_stock_log 
SET 
    quantity = ABS(quantity),
    remark = CONCAT(IFNULL(remark, ''), ' [系统修复：原数量为负值]')
WHERE quantity < 0;

-- Verify the fix
SELECT CONCAT('Fixed negative quantities: ', ROW_COUNT(), ' records updated') as fix_result;

-- =============================================
-- Step 3: Clean up orphaned relationships
-- =============================================
SELECT '=== Clean up orphaned relationships ===' as info;

-- Remove orphaned role-menu relationships
DELETE rm FROM sys_role_menu rm
LEFT JOIN sys_role r ON rm.role_id = r.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IS NULL OR m.menu_id IS NULL;

SELECT CONCAT('Removed orphaned role-menu relationships: ', ROW_COUNT(), ' records deleted') as fix_result;

-- Remove orphaned user-role relationships
DELETE ur FROM sys_user_role ur
LEFT JOIN sys_user u ON ur.user_id = u.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.user_id IS NULL OR r.role_id IS NULL;

SELECT CONCAT('Removed orphaned user-role relationships: ', ROW_COUNT(), ' records deleted') as fix_result;

-- =============================================
-- Step 4: Add missing constraints and indexes
-- =============================================
SELECT '=== Add missing constraints and indexes ===' as info;

-- Add foreign key constraints for data integrity
ALTER TABLE sys_role_menu 
ADD CONSTRAINT fk_role_menu_role_id 
FOREIGN KEY (role_id) REFERENCES sys_role(role_id) ON DELETE CASCADE;

ALTER TABLE sys_role_menu 
ADD CONSTRAINT fk_role_menu_menu_id 
FOREIGN KEY (menu_id) REFERENCES sys_menu(menu_id) ON DELETE CASCADE;

ALTER TABLE sys_user_role 
ADD CONSTRAINT fk_user_role_user_id 
FOREIGN KEY (user_id) REFERENCES sys_user(user_id) ON DELETE CASCADE;

ALTER TABLE sys_user_role 
ADD CONSTRAINT fk_user_role_role_id 
FOREIGN KEY (role_id) REFERENCES sys_role(role_id) ON DELETE CASCADE;

-- Add indexes for better performance (ignore errors if indexes already exist)
CREATE INDEX idx_sys_system_log_user_time ON sys_system_log(user_name, create_time);
CREATE INDEX idx_sys_security_log_user_time ON sys_security_log(user_name, event_time);
CREATE INDEX idx_sys_stock_log_product_time ON sys_stock_log(product_id, operation_time);
CREATE INDEX idx_sys_stock_log_warehouse_time ON sys_stock_log(warehouse_id, operation_time);

SELECT 'Added foreign key constraints and performance indexes' as fix_result;

-- =============================================
-- Step 5: Create data integrity triggers
-- =============================================
SELECT '=== Create data integrity triggers ===' as info;

DELIMITER //

-- Trigger to validate stock log data before insert
CREATE TRIGGER IF NOT EXISTS tr_sys_stock_log_before_insert
BEFORE INSERT ON sys_stock_log
FOR EACH ROW
BEGIN
    -- Ensure quantity is not negative
    IF NEW.quantity < 0 THEN
        SET NEW.quantity = ABS(NEW.quantity);
        SET NEW.remark = CONCAT(IFNULL(NEW.remark, ''), ' [自动修复：负数量]');
    END IF;
    
    -- Ensure operation_type is valid
    IF NEW.operation_type NOT IN ('IN', 'OUT', 'TRANSFER', 'ADJUST', 'CHECK') THEN
        SET NEW.operation_type = 'ADJUST';
        SET NEW.remark = CONCAT(IFNULL(NEW.remark, ''), ' [自动修复：无效操作类型]');
    END IF;
    
    -- Set default values if missing
    IF NEW.operator IS NULL OR NEW.operator = '' THEN
        SET NEW.operator = 'system';
    END IF;
    
    IF NEW.operation_time IS NULL THEN
        SET NEW.operation_time = NOW();
    END IF;
END //

-- Trigger to validate system log data before insert
CREATE TRIGGER IF NOT EXISTS tr_sys_system_log_before_insert
BEFORE INSERT ON sys_system_log
FOR EACH ROW
BEGIN
    -- Ensure user_name is not empty
    IF NEW.user_name IS NULL OR NEW.user_name = '' THEN
        SET NEW.user_name = 'unknown';
    END IF;
    
    -- Set default log_type if missing
    IF NEW.log_type IS NULL OR NEW.log_type = '' THEN
        SET NEW.log_type = 'SYSTEM';
    END IF;
    
    -- Set create_time if missing
    IF NEW.create_time IS NULL THEN
        SET NEW.create_time = NOW();
    END IF;
END //

-- Trigger to validate security log data before insert
CREATE TRIGGER IF NOT EXISTS tr_sys_security_log_before_insert
BEFORE INSERT ON sys_security_log
FOR EACH ROW
BEGIN
    -- Ensure user_name is not empty
    IF NEW.user_name IS NULL OR NEW.user_name = '' THEN
        SET NEW.user_name = 'anonymous';
    END IF;
    
    -- Set default event_type if missing
    IF NEW.event_type IS NULL OR NEW.event_type = '' THEN
        SET NEW.event_type = 'LOGIN';
    END IF;
    
    -- Set create_time if missing
    IF NEW.create_time IS NULL THEN
        SET NEW.create_time = NOW();
    END IF;
END //

DELIMITER ;

SELECT 'Created data integrity triggers' as fix_result;

-- =============================================
-- Step 6: Create data consistency check procedures
-- =============================================
SELECT '=== Create data consistency check procedures ===' as info;

DELIMITER //

-- Procedure to check and fix data consistency
CREATE PROCEDURE IF NOT EXISTS sp_check_data_consistency()
BEGIN
    DECLARE inconsistency_count INT DEFAULT 0;
    
    -- Check for duplicate records in system log
    SELECT COUNT(*) INTO inconsistency_count
    FROM (
        SELECT user_name, module, operation_time, COUNT(*) as cnt
        FROM sys_system_log
        GROUP BY user_name, module, operation_time
        HAVING cnt > 1
    ) duplicates;
    
    IF inconsistency_count > 0 THEN
        INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
        VALUES ('CONSISTENCY_CHECK', 'sys_system_log', 'Check for duplicate records', inconsistency_count, 'FAIL', 
                CONCAT('Found ', inconsistency_count, ' potential duplicate records'));
    END IF;
    
    -- Check for missing required fields
    SELECT COUNT(*) INTO inconsistency_count
    FROM sys_stock_log
    WHERE warehouse_id IS NULL OR product_id IS NULL;
    
    IF inconsistency_count > 0 THEN
        INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status, error_message)
        VALUES ('CONSISTENCY_CHECK', 'sys_stock_log', 'Check for missing required fields', inconsistency_count, 'FAIL', 
                CONCAT('Found ', inconsistency_count, ' records with missing required fields'));
    END IF;
    
    -- Log successful completion
    INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, status)
    VALUES ('CONSISTENCY_CHECK', 'ALL_TABLES', 'Data consistency check completed', 'PASS');
END //

DELIMITER ;

-- =============================================
-- Step 7: Run validation after fixes
-- =============================================
SELECT '=== Run validation after fixes ===' as info;

-- Clear previous validation results for re-validation
DELETE FROM sys_data_validation_log WHERE DATE(validation_time) = CURDATE();

-- Re-run data migration validation
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, expected_count, actual_count, status)
SELECT 
    'DATA_MIGRATION_FIXED' as validation_type,
    'sys_security_log' as table_name,
    'Compare record count with original sys_logininfor after fix' as validation_rule,
    (SELECT COUNT(*) FROM sys_logininfor) as expected_count,
    (SELECT COUNT(*) FROM sys_security_log) as actual_count,
    CASE 
        WHEN (SELECT COUNT(*) FROM sys_logininfor) = (SELECT COUNT(*) FROM sys_security_log) THEN 'PASS'
        ELSE 'FAIL'
    END as status;

-- Re-run negative quantity validation
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status)
SELECT 
    'DATA_CONSISTENCY_FIXED' as validation_type,
    'sys_stock_log' as table_name,
    'Check for negative quantities after fix' as validation_rule,
    COUNT(*) as actual_count,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_stock_log 
WHERE quantity < 0;

-- Re-run orphaned relationships validation
INSERT INTO sys_data_validation_log (validation_type, table_name, validation_rule, actual_count, status)
SELECT 
    'REFERENTIAL_INTEGRITY_FIXED' as validation_type,
    'sys_role_menu' as table_name,
    'Check for orphaned role-menu relationships after fix' as validation_rule,
    COUNT(*) as actual_count,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_role_menu rm
LEFT JOIN sys_role r ON rm.role_id = r.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IS NULL OR m.menu_id IS NULL;

-- =============================================
-- Step 8: Generate fix summary report
-- =============================================
SELECT '=== Generate fix summary report ===' as info;

-- Display validation results after fixes
SELECT 
    validation_type,
    table_name,
    validation_rule,
    expected_count,
    actual_count,
    status,
    error_message
FROM sys_data_validation_log
WHERE DATE(validation_time) = CURDATE()
ORDER BY validation_time DESC;

-- Display overall fix summary
SELECT 
    COUNT(*) as total_validations,
    COUNT(CASE WHEN status = 'PASS' THEN 1 END) as passed,
    COUNT(CASE WHEN status = 'FAIL' THEN 1 END) as failed,
    ROUND(COUNT(CASE WHEN status = 'PASS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM sys_data_validation_log
WHERE DATE(validation_time) = CURDATE();

-- Commit transaction
COMMIT;

SELECT '=== Data integrity fixes completed ===' as result;
SELECT 'All identified data integrity issues have been addressed' as summary;
