-- =============================================
-- Permission Control and Role Assignment Standardization Script
-- Target: Establish complete permission control system, configure role templates, implement fine-grained permission management
-- Please backup database before execution!
-- =============================================

-- Set character set
SET NAMES utf8mb4;

-- Start transaction
START TRANSACTION;

-- =============================================
-- Step 1: Create role templates for log management
-- =============================================
SELECT '=== Create role templates for log management ===' as info;

-- Insert log management role templates if not exist
INSERT IGNORE INTO sys_role (role_name, role_key, role_sort, data_scope, menu_check_strictly, dept_check_strictly, status, del_flag, create_by, create_time, remark) VALUES
('日志管理员', 'log_admin', 10, '1', 1, 1, '0', '0', 'admin', NOW(), '日志管理员角色，拥有所有日志管理权限'),
('日志查看员', 'log_viewer', 11, '2', 1, 1, '0', '0', 'admin', NOW(), '日志查看员角色，只能查看和导出日志'),
('系统日志管理员', 'system_log_admin', 12, '1', 1, 1, '0', '0', 'admin', NOW(), '系统日志管理员，管理系统操作日志'),
('安全日志管理员', 'security_log_admin', 13, '1', 1, 1, '0', '0', 'admin', NOW(), '安全日志管理员，管理登录和安全日志'),
('出入库日志管理员', 'stock_log_admin', 14, '1', 1, 1, '0', '0', 'admin', NOW(), '出入库日志管理员，管理库存变动日志'),
('错误日志管理员', 'error_log_admin', 15, '1', 1, 1, '0', '0', 'admin', NOW(), '错误日志管理员，管理系统错误日志');

-- =============================================
-- Step 2: Configure role permissions for log management
-- =============================================
SELECT '=== Configure role permissions ===' as info;

-- Get role IDs for permission assignment
SET @log_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'log_admin' LIMIT 1);
SET @log_viewer_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'log_viewer' LIMIT 1);
SET @system_log_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'system_log_admin' LIMIT 1);
SET @security_log_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'security_log_admin' LIMIT 1);
SET @stock_log_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'stock_log_admin' LIMIT 1);
SET @error_log_admin_role_id = (SELECT role_id FROM sys_role WHERE role_key = 'error_log_admin' LIMIT 1);

-- Clear existing role-menu relationships for log roles
DELETE FROM sys_role_menu WHERE role_id IN (@log_admin_role_id, @log_viewer_role_id, @system_log_admin_role_id, @security_log_admin_role_id, @stock_log_admin_role_id, @error_log_admin_role_id);

-- Assign permissions to Log Admin (full access to all log functions)
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT @log_admin_role_id, menu_id FROM sys_menu 
WHERE parent_id = 34 OR menu_id = 34 OR parent_id IN (35, 36, 3787, 4201);

-- Assign permissions to Log Viewer (read-only access)
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT @log_viewer_role_id, menu_id FROM sys_menu 
WHERE (parent_id = 34 OR menu_id = 34 OR parent_id IN (35, 36, 3787, 4201))
AND (perms LIKE '%:list' OR perms LIKE '%:query' OR perms LIKE '%:export' OR perms IS NULL);

-- Assign permissions to System Log Admin (system log only)
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT @system_log_admin_role_id, menu_id FROM sys_menu 
WHERE menu_id = 34 OR menu_id = 35 OR parent_id = 35;

-- Assign permissions to Security Log Admin (security log only)
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT @security_log_admin_role_id, menu_id FROM sys_menu 
WHERE menu_id = 34 OR menu_id = 3787 OR parent_id = 3787;

-- Assign permissions to Stock Log Admin (stock log only)
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT @stock_log_admin_role_id, menu_id FROM sys_menu 
WHERE menu_id = 34 OR menu_id = 36 OR parent_id = 36;

-- Assign permissions to Error Log Admin (error log only)
INSERT IGNORE INTO sys_role_menu (role_id, menu_id)
SELECT @error_log_admin_role_id, menu_id FROM sys_menu 
WHERE menu_id = 34 OR menu_id = 4201 OR parent_id = 4201;

-- =============================================
-- Step 3: Create data permission templates
-- =============================================
SELECT '=== Create data permission templates ===' as info;

-- Update role data scopes for fine-grained control
UPDATE sys_role SET data_scope = '1' WHERE role_key IN ('log_admin', 'system_log_admin', 'security_log_admin', 'error_log_admin'); -- All data
UPDATE sys_role SET data_scope = '2' WHERE role_key = 'log_viewer'; -- Custom data scope
UPDATE sys_role SET data_scope = '4' WHERE role_key = 'stock_log_admin'; -- Department and sub-departments

-- =============================================
-- Step 4: Create permission validation rules
-- =============================================
SELECT '=== Create permission validation rules ===' as info;

-- Create a view for permission validation
CREATE OR REPLACE VIEW v_user_log_permissions AS
SELECT 
    u.user_id,
    u.user_name,
    r.role_key,
    r.role_name,
    m.menu_id,
    m.menu_name,
    m.perms,
    m.path,
    CASE 
        WHEN m.perms LIKE '%:list' THEN 'READ'
        WHEN m.perms LIKE '%:query' THEN 'READ'
        WHEN m.perms LIKE '%:export' THEN 'READ'
        WHEN m.perms LIKE '%:remove' THEN 'DELETE'
        WHEN m.perms LIKE '%:clean' THEN 'DELETE'
        WHEN m.perms LIKE '%:unlock' THEN 'WRITE'
        ELSE 'OTHER'
    END as permission_type
FROM sys_user u
JOIN sys_user_role ur ON u.user_id = ur.user_id
JOIN sys_role r ON ur.role_id = r.role_id
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE m.parent_id IN (34, 35, 36, 3787, 4201) OR m.menu_id = 34
AND u.status = '0' AND r.status = '0' AND m.status = '0';

-- =============================================
-- Step 5: Configure department-based permissions
-- =============================================
SELECT '=== Configure department-based permissions ===' as info;

-- Create department permission mapping for stock log admin
-- This would typically be customized based on actual department structure
INSERT IGNORE INTO sys_role_dept (role_id, dept_id)
SELECT @stock_log_admin_role_id, dept_id FROM sys_dept WHERE dept_name LIKE '%仓库%' OR dept_name LIKE '%库存%';

-- =============================================
-- Step 6: Create audit trail for permission changes
-- =============================================
SELECT '=== Create audit trail ===' as info;

-- Log permission configuration changes
INSERT INTO sys_oper_log (title, business_type, method, request_method, operator_type, oper_name, dept_name, oper_url, oper_ip, oper_location, oper_param, json_result, status, error_msg, oper_time, cost_time)
VALUES ('权限配置', 4, 'permission_role_standardization.sql', 'SQL', 1, 'admin', '系统管理', '/system/permission/config', '127.0.0.1', '内网IP', 
'配置日志管理权限角色模板', '成功创建6个日志管理角色模板并配置相应权限', 0, '', NOW(), 0);

-- =============================================
-- Step 7: Verify permission configuration
-- =============================================
SELECT '=== Verify permission configuration ===' as info;

-- Display role summary
SELECT 
    r.role_id,
    r.role_name,
    r.role_key,
    r.data_scope,
    COUNT(rm.menu_id) as permission_count
FROM sys_role r
LEFT JOIN sys_role_menu rm ON r.role_id = rm.role_id
WHERE r.role_key LIKE '%log%'
GROUP BY r.role_id, r.role_name, r.role_key, r.data_scope
ORDER BY r.role_sort;

-- Display permission details for each role
SELECT 
    r.role_name,
    m.menu_name,
    m.perms,
    CASE 
        WHEN m.menu_type = 'M' THEN '菜单'
        WHEN m.menu_type = 'C' THEN '页面'
        WHEN m.menu_type = 'F' THEN '按钮'
        ELSE '其他'
    END as menu_type_desc
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_key LIKE '%log%'
ORDER BY r.role_sort, m.order_num;

-- Count total permissions by type
SELECT 
    '权限统计' as info,
    COUNT(CASE WHEN m.perms LIKE '%:list' OR m.perms LIKE '%:query' THEN 1 END) as read_permissions,
    COUNT(CASE WHEN m.perms LIKE '%:remove' OR m.perms LIKE '%:clean' THEN 1 END) as delete_permissions,
    COUNT(CASE WHEN m.perms LIKE '%:export' THEN 1 END) as export_permissions,
    COUNT(CASE WHEN m.perms LIKE '%:unlock' THEN 1 END) as special_permissions
FROM sys_menu m
WHERE m.parent_id IN (35, 36, 3787, 4201) AND m.menu_type = 'F';

-- Commit transaction
COMMIT;

SELECT '=== Permission control and role assignment standardization completed ===' as result;
SELECT 'Created 6 role templates with fine-grained permissions for log management' as summary;
SELECT 'Roles: log_admin, log_viewer, system_log_admin, security_log_admin, stock_log_admin, error_log_admin' as roles;
