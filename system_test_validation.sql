-- =============================================
-- System Test and Validation Script
-- Target: Comprehensive testing of all log functions, permission control validation, system stability assurance
-- =============================================

-- Set character set
SET NAMES utf8mb4;

-- Start transaction
START TRANSACTION;

-- =============================================
-- Step 1: Create test results table
-- =============================================
SELECT '=== Create test results table ===' as info;

CREATE TABLE IF NOT EXISTS sys_test_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    test_category VARCHAR(50) NOT NULL COMMENT '测试类别',
    test_name VARCHAR(100) NOT NULL COMMENT '测试名称',
    test_description VARCHAR(200) COMMENT '测试描述',
    expected_result VARCHAR(500) COMMENT '期望结果',
    actual_result VARCHAR(500) COMMENT '实际结果',
    status VARCHAR(20) DEFAULT 'PENDING' COMMENT '状态：PASS,FAIL,PENDING',
    error_message TEXT COMMENT '错误信息',
    execution_time DECIMAL(10,3) DEFAULT 0 COMMENT '执行时间(秒)',
    test_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '测试时间',
    INDEX idx_test_category (test_category),
    INDEX idx_status (status),
    INDEX idx_test_time (test_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统测试结果表';

-- Clear previous test results
DELETE FROM sys_test_results WHERE DATE(test_time) = CURDATE();

-- =============================================
-- Step 2: Test database structure and data integrity
-- =============================================
SELECT '=== Test database structure and data integrity ===' as info;

-- Test 1: Verify all log tables exist
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'DATABASE_STRUCTURE' as test_category,
    'Log Tables Existence' as test_name,
    'Verify all standardized log tables exist' as test_description,
    '4 tables: sys_system_log, sys_security_log, sys_stock_log, sys_error_log' as expected_result,
    CONCAT(COUNT(*), ' tables found: ', GROUP_CONCAT(TABLE_NAME)) as actual_result,
    CASE WHEN COUNT(*) = 4 THEN 'PASS' ELSE 'FAIL' END as status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND TABLE_NAME IN ('sys_system_log', 'sys_security_log', 'sys_stock_log', 'sys_error_log');

-- Test 2: Verify menu structure
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'DATABASE_STRUCTURE' as test_category,
    'Menu Structure' as test_name,
    'Verify standardized menu structure exists' as test_description,
    '4 main log menus under parent 34' as expected_result,
    CONCAT(COUNT(*), ' menus found') as actual_result,
    CASE WHEN COUNT(*) = 4 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_menu 
WHERE parent_id = 34 AND menu_type = 'C';

-- Test 3: Verify role templates
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'DATABASE_STRUCTURE' as test_category,
    'Role Templates' as test_name,
    'Verify log management role templates exist' as test_description,
    '6 role templates with log_ prefix' as expected_result,
    CONCAT(COUNT(*), ' roles found') as actual_result,
    CASE WHEN COUNT(*) = 6 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_role 
WHERE role_key LIKE '%log%';

-- =============================================
-- Step 3: Test data consistency and integrity
-- =============================================
SELECT '=== Test data consistency and integrity ===' as info;

-- Test 4: Check for data consistency in system log
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'DATA_INTEGRITY' as test_category,
    'System Log Data Consistency' as test_name,
    'Check for NULL values in critical fields' as test_description,
    '0 records with NULL user_name' as expected_result,
    CONCAT(COUNT(*), ' records with NULL user_name') as actual_result,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_system_log 
WHERE user_name IS NULL OR user_name = '';

-- Test 5: Check security log data integrity
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'DATA_INTEGRITY' as test_category,
    'Security Log Data Integrity' as test_name,
    'Check for valid event types' as test_description,
    'All records have valid event_type' as expected_result,
    CONCAT(COUNT(*), ' records with invalid event_type') as actual_result,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_security_log 
WHERE event_type IS NULL OR event_type = '';

-- Test 6: Check stock log data integrity
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'DATA_INTEGRITY' as test_category,
    'Stock Log Data Integrity' as test_name,
    'Check for negative quantities' as test_description,
    '0 records with negative quantity' as expected_result,
    CONCAT(COUNT(*), ' records with negative quantity') as actual_result,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_stock_log 
WHERE quantity < 0;

-- =============================================
-- Step 4: Test permission control system
-- =============================================
SELECT '=== Test permission control system ===' as info;

-- Test 7: Verify role-menu relationships
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'PERMISSION_CONTROL' as test_category,
    'Role-Menu Relationships' as test_name,
    'Check for orphaned role-menu relationships' as test_description,
    '0 orphaned relationships' as expected_result,
    CONCAT(COUNT(*), ' orphaned relationships') as actual_result,
    CASE WHEN COUNT(*) = 0 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_role_menu rm
LEFT JOIN sys_role r ON rm.role_id = r.role_id
LEFT JOIN sys_menu m ON rm.menu_id = m.menu_id
WHERE r.role_id IS NULL OR m.menu_id IS NULL;

-- Test 8: Verify permission completeness for log admin role
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'PERMISSION_CONTROL' as test_category,
    'Log Admin Permissions' as test_name,
    'Verify log admin has all required permissions' as test_description,
    'At least 15 permissions assigned' as expected_result,
    CONCAT(COUNT(*), ' permissions assigned') as actual_result,
    CASE WHEN COUNT(*) >= 15 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_role_menu rm
JOIN sys_role r ON rm.role_id = r.role_id
WHERE r.role_key = 'log_admin';

-- =============================================
-- Step 5: Test system functionality
-- =============================================
SELECT '=== Test system functionality ===' as info;

-- Test 9: Test data validation triggers
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'SYSTEM_FUNCTIONALITY' as test_category,
    'Data Validation Triggers' as test_name,
    'Check if data validation triggers exist' as test_description,
    '3 triggers for log tables' as expected_result,
    CONCAT(COUNT(*), ' triggers found') as actual_result,
    CASE WHEN COUNT(*) >= 3 THEN 'PASS' ELSE 'FAIL' END as status
FROM INFORMATION_SCHEMA.TRIGGERS 
WHERE TRIGGER_SCHEMA = 'warehouse_system' 
AND TRIGGER_NAME LIKE '%sys_%log%';

-- Test 10: Test stored procedures
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'SYSTEM_FUNCTIONALITY' as test_category,
    'Stored Procedures' as test_name,
    'Check if validation procedures exist' as test_description,
    'At least 2 procedures for validation' as expected_result,
    CONCAT(COUNT(*), ' procedures found') as actual_result,
    CASE WHEN COUNT(*) >= 2 THEN 'PASS' ELSE 'FAIL' END as status
FROM INFORMATION_SCHEMA.ROUTINES 
WHERE ROUTINE_SCHEMA = 'warehouse_system' 
AND ROUTINE_TYPE = 'PROCEDURE'
AND ROUTINE_NAME LIKE '%validation%';

-- =============================================
-- Step 6: Performance and optimization tests
-- =============================================
SELECT '=== Test performance and optimization ===' as info;

-- Test 11: Check for proper indexing
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'PERFORMANCE' as test_category,
    'Database Indexing' as test_name,
    'Check for performance indexes on log tables' as test_description,
    'At least 8 indexes on log tables' as expected_result,
    CONCAT(COUNT(*), ' indexes found') as actual_result,
    CASE WHEN COUNT(*) >= 8 THEN 'PASS' ELSE 'FAIL' END as status
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND TABLE_NAME LIKE '%log%'
AND INDEX_NAME != 'PRIMARY';

-- Test 12: Check table sizes and performance
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'PERFORMANCE' as test_category,
    'Table Performance' as test_name,
    'Check if log tables have reasonable sizes' as test_description,
    'All tables under 100MB' as expected_result,
    CONCAT('Largest table: ', MAX(ROUND(data_length/1024/1024, 2)), 'MB') as actual_result,
    CASE WHEN MAX(data_length) < 100*1024*1024 THEN 'PASS' ELSE 'FAIL' END as status
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'warehouse_system' 
AND TABLE_NAME LIKE '%log%';

-- =============================================
-- Step 7: Security and access control tests
-- =============================================
SELECT '=== Test security and access control ===' as info;

-- Test 13: Verify role separation
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'SECURITY' as test_category,
    'Role Separation' as test_name,
    'Verify different roles have different permission sets' as test_description,
    'Each role has unique permission count' as expected_result,
    CONCAT('Roles with permissions: ', COUNT(DISTINCT r.role_id)) as actual_result,
    CASE WHEN COUNT(DISTINCT r.role_id) >= 6 THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_role r
JOIN sys_role_menu rm ON r.role_id = rm.role_id
WHERE r.role_key LIKE '%log%';

-- Test 14: Check data scope configuration
INSERT INTO sys_test_results (test_category, test_name, test_description, expected_result, actual_result, status)
SELECT 
    'SECURITY' as test_category,
    'Data Scope Configuration' as test_name,
    'Verify roles have appropriate data scopes' as test_description,
    'All log roles have valid data_scope values' as expected_result,
    CONCAT(COUNT(*), ' roles with valid data_scope') as actual_result,
    CASE WHEN COUNT(*) = (SELECT COUNT(*) FROM sys_role WHERE role_key LIKE '%log%') THEN 'PASS' ELSE 'FAIL' END as status
FROM sys_role 
WHERE role_key LIKE '%log%' 
AND data_scope IN ('1', '2', '3', '4', '5');

-- =============================================
-- Step 8: Generate comprehensive test report
-- =============================================
SELECT '=== Generate comprehensive test report ===' as info;

-- Overall test summary
SELECT 
    'TEST_SUMMARY' as report_section,
    test_category,
    COUNT(*) as total_tests,
    COUNT(CASE WHEN status = 'PASS' THEN 1 END) as passed_tests,
    COUNT(CASE WHEN status = 'FAIL' THEN 1 END) as failed_tests,
    ROUND(COUNT(CASE WHEN status = 'PASS' THEN 1 END) * 100.0 / COUNT(*), 2) as success_rate
FROM sys_test_results
WHERE DATE(test_time) = CURDATE()
GROUP BY test_category
ORDER BY test_category;

-- Failed tests detail
SELECT 
    'FAILED_TESTS' as report_section,
    test_category,
    test_name,
    test_description,
    expected_result,
    actual_result,
    error_message
FROM sys_test_results
WHERE DATE(test_time) = CURDATE() AND status = 'FAIL'
ORDER BY test_category, test_name;

-- Overall system health score
SELECT 
    'SYSTEM_HEALTH' as report_section,
    COUNT(*) as total_tests,
    COUNT(CASE WHEN status = 'PASS' THEN 1 END) as passed_tests,
    COUNT(CASE WHEN status = 'FAIL' THEN 1 END) as failed_tests,
    ROUND(COUNT(CASE WHEN status = 'PASS' THEN 1 END) * 100.0 / COUNT(*), 2) as overall_health_score,
    CASE 
        WHEN COUNT(CASE WHEN status = 'PASS' THEN 1 END) * 100.0 / COUNT(*) >= 95 THEN 'EXCELLENT'
        WHEN COUNT(CASE WHEN status = 'PASS' THEN 1 END) * 100.0 / COUNT(*) >= 85 THEN 'GOOD'
        WHEN COUNT(CASE WHEN status = 'PASS' THEN 1 END) * 100.0 / COUNT(*) >= 70 THEN 'FAIR'
        ELSE 'POOR'
    END as health_status
FROM sys_test_results
WHERE DATE(test_time) = CURDATE();

-- Commit transaction
COMMIT;

SELECT '=== System test and validation completed ===' as result;
SELECT 'Comprehensive testing of log management system finished' as summary;
