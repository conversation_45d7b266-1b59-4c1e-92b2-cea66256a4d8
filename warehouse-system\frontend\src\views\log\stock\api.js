import request from '@/utils/request'

// 查询出入库日志列表
export function listStockLog(query) {
  return request({
    url: '/log/inventory/list',
    method: 'get',
    params: query
  })
}

// 获取出入库日志详细信息
export function getStockLog(logId) {
  return request({
    url: '/log/inventory/' + logId,
    method: 'get'
  })
}

// 删除出入库日志
export function delStockLog(logIds) {
  return request({
    url: '/log/inventory/' + logIds,
    method: 'delete'
  })
}

// 导出出入库日志
export function exportStockLog(query) {
  return request({
    url: '/log/inventory/export',
    method: 'get',
    params: query
  })
}

// 获取出入库日志统计信息
export function getStockLogStats(query) {
  return request({
    url: '/log/inventory/stats',
    method: 'get',
    params: query
  })
}

// 获取出入库日志趋势数据
export function getStockLogTrend(query) {
  return request({
    url: '/log/inventory/trend',
    method: 'get',
    params: query
  })
}

// 获取库存变动分析
export function getStockChangeAnalysis(query) {
  return request({
    url: '/log/inventory/changeAnalysis',
    method: 'get',
    params: query
  })
}

// 获取仓库操作统计
export function getWarehouseOperationStats(query) {
  return request({
    url: '/log/inventory/warehouseStats',
    method: 'get',
    params: query
  })
}

// 获取商品操作统计
export function getProductOperationStats(query) {
  return request({
    url: '/log/inventory/productStats',
    method: 'get',
    params: query
  })
}

// 获取操作员统计
export function getOperatorStats(query) {
  return request({
    url: '/log/inventory/operatorStats',
    method: 'get',
    params: query
  })
}
