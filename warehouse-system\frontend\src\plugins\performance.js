/**
 * 性能优化和用户体验提升插件
 * 全局配置性能监控、错误处理、加载状态管理
 */

import Vue from 'vue'
import { 
  performanceMonitor, 
  errorHandler, 
  loadingManager, 
  cacheManager,
  utils 
} from '@/utils/performance'

// 全局性能配置
const PerformancePlugin = {
  install(Vue, options = {}) {
    // 默认配置
    const defaultOptions = {
      enablePerformanceMonitor: true,
      enableErrorHandler: true,
      enableLoadingManager: true,
      enableCache: true,
      showPerformanceInfo: process.env.NODE_ENV === 'development',
      apiTimeout: 30000,
      retryCount: 3,
      retryDelay: 1000
    }

    const config = { ...defaultOptions, ...options }

    // 添加全局属性
    Vue.prototype.$performance = performanceMonitor
    Vue.prototype.$errorHandler = errorHandler
    Vue.prototype.$loadingManager = loadingManager
    Vue.prototype.$cache = cacheManager
    Vue.prototype.$utils = utils

    // 全局加载状态管理
    if (config.enableLoadingManager) {
      Vue.prototype.$loading = {
        show(key = 'global') {
          loadingManager.setLoading(key, true)
        },
        hide(key = 'global') {
          loadingManager.setLoading(key, false)
        },
        isLoading(key = 'global') {
          return loadingManager.isLoading(key)
        }
      }
    }

    // 全局错误处理增强
    if (config.enableErrorHandler) {
      Vue.config.errorHandler = (err, vm, info) => {
        console.error('Vue Error:', err, info)
        
        errorHandler.recordError({
          type: 'vue',
          message: err.message,
          stack: err.stack,
          info: info,
          component: vm ? vm.$options.name : 'Unknown',
          timestamp: Date.now()
        })

        // 显示用户友好的错误信息
        if (vm && vm.$message) {
          vm.$message.error('页面出现异常，请刷新重试')
        }
      }

      // Promise 错误处理
      window.addEventListener('unhandledrejection', event => {
        console.error('Unhandled Promise Rejection:', event.reason)
        
        errorHandler.recordError({
          type: 'promise',
          message: event.reason ? event.reason.message : '未知Promise错误',
          stack: event.reason ? event.reason.stack : '',
          timestamp: Date.now()
        })

        // 阻止默认的控制台错误输出
        event.preventDefault()
      })
    }

    // 全局API拦截器
    Vue.prototype.$safeRequest = async function(requestFn, options = {}) {
      const {
        loadingKey = 'request',
        errorMessage = '请求失败，请稍后重试',
        showLoading = true,
        useCache = false,
        cacheKey = '',
        cacheTTL = 300000,
        retryCount = config.retryCount,
        retryDelay = config.retryDelay
      } = options

      // 检查缓存
      if (useCache && cacheKey && cacheManager.has(cacheKey)) {
        const cached = cacheManager.get(cacheKey)
        return cached.value
      }

      let attempt = 0
      const maxAttempts = retryCount + 1

      while (attempt < maxAttempts) {
        try {
          if (showLoading) {
            this.$loading.show(loadingKey)
          }

          const startTime = performance.now()
          const result = await Promise.race([
            requestFn(),
            new Promise((_, reject) => 
              setTimeout(() => reject(new Error('请求超时')), config.apiTimeout)
            )
          ])
          const endTime = performance.now()

          // 记录性能数据
          if (config.enablePerformanceMonitor) {
            performanceMonitor.recordUserAction('api_request', {
              duration: endTime - startTime,
              success: true,
              attempt: attempt + 1
            })
          }

          // 缓存结果
          if (useCache && cacheKey) {
            cacheManager.set(cacheKey, result, cacheTTL)
          }

          return result

        } catch (error) {
          attempt++
          
          if (attempt >= maxAttempts) {
            // 记录错误
            errorHandler.recordError({
              type: 'api',
              message: error.message,
              stack: error.stack,
              timestamp: Date.now(),
              attempts: attempt
            })

            if (this.$message) {
              this.$message.error(errorMessage)
            }
            throw error
          }

          // 等待后重试
          if (attempt < maxAttempts) {
            await new Promise(resolve => setTimeout(resolve, retryDelay * attempt))
          }
        } finally {
          if (showLoading) {
            this.$loading.hide(loadingKey)
          }
        }
      }
    }

    // 全局性能监控方法
    Vue.prototype.$trackPerformance = function(action, details = {}) {
      if (config.enablePerformanceMonitor) {
        performanceMonitor.recordUserAction(action, {
          ...details,
          component: this.$options.name,
          route: this.$route ? this.$route.path : 'unknown'
        })
      }
    }

    // 全局混入 - 添加性能监控
    Vue.mixin({
      created() {
        if (config.showPerformanceInfo) {
          this._componentCreateTime = performance.now()
        }
      },

      mounted() {
        if (config.showPerformanceInfo) {
          const mountTime = performance.now() - this._componentCreateTime
          console.log(`[Performance] ${this.$options.name} 组件挂载时间: ${mountTime.toFixed(2)}ms`)
        }

        // 记录页面访问
        if (config.enablePerformanceMonitor && this.$options.name) {
          this.$trackPerformance('component_mounted', {
            componentName: this.$options.name
          })
        }
      },

      beforeDestroy() {
        // 清理组件相关的加载状态
        if (this.$options.name) {
          loadingManager.setLoading(this.$options.name, false)
        }
      }
    })

    // 路由性能监控
    if (config.enablePerformanceMonitor && options.router) {
      options.router.beforeEach((to, from, next) => {
        performanceMonitor.recordUserAction('route_change', {
          from: from.path,
          to: to.path,
          timestamp: Date.now()
        })
        next()
      })
    }

    // 开发环境性能信息显示
    if (config.showPerformanceInfo) {
      // 添加性能信息到控制台
      setInterval(() => {
        const metrics = performanceMonitor.getMetrics()
        console.group('📊 性能监控信息')
        console.log('页面加载时间:', utils.formatTime(metrics.pageLoadTime))
        console.log('平均API响应时间:', utils.formatTime(metrics.averageApiResponseTime))
        console.log('错误数量:', metrics.errorCount)
        console.log('慢API调用:', metrics.slowApiCalls.length)
        console.log('缓存大小:', cacheManager.size())
        console.log('网络状态:', utils.getNetworkStatus())
        console.groupEnd()
      }, 30000) // 每30秒输出一次
    }

    // 页面可见性变化处理
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        // 页面隐藏时暂停一些操作
        performanceMonitor.recordUserAction('page_hidden')
      } else {
        // 页面显示时恢复操作
        performanceMonitor.recordUserAction('page_visible')
      }
    })

    // 网络状态变化处理
    window.addEventListener('online', () => {
      Vue.prototype.$message && Vue.prototype.$message.success('网络连接已恢复')
      performanceMonitor.recordUserAction('network_online')
    })

    window.addEventListener('offline', () => {
      Vue.prototype.$message && Vue.prototype.$message.warning('网络连接已断开')
      performanceMonitor.recordUserAction('network_offline')
    })

    console.log('🚀 性能优化插件已启用')
  }
}

export default PerformancePlugin
