import request from '@/utils/request'

// 查询错误日志列表
export function listErrorLog(query) {
  return request({
    url: '/monitor/operlog/list',
    method: 'get',
    params: {
      ...query,
      status: '1' // 只查询失败的操作记录
    }
  })
}

// 获取错误日志详细信息
export function getErrorLog(logId) {
  return request({
    url: '/monitor/operlog/' + logId,
    method: 'get'
  })
}

// 删除错误日志
export function delErrorLog(logIds) {
  return request({
    url: '/monitor/operlog/' + logIds,
    method: 'delete'
  })
}

// 清空错误日志
export function cleanErrorLog() {
  return request({
    url: '/monitor/operlog/clean',
    method: 'delete',
    params: {
      status: '1' // 只清空失败的记录
    }
  })
}

// 导出错误日志
export function exportErrorLog(query) {
  return request({
    url: '/monitor/operlog/export',
    method: 'get',
    params: {
      ...query,
      status: '1' // 只导出失败的记录
    }
  })
}

// 标记错误已解决
export function markErrorResolved(logId, resolveNote) {
  return request({
    url: '/log/error/resolve/' + logId,
    method: 'put',
    data: {
      resolveNote: resolveNote
    }
  })
}

// 批量标记错误已解决
export function batchMarkErrorResolved(logIds, resolveNote) {
  return request({
    url: '/log/error/batchResolve',
    method: 'put',
    data: {
      logIds: logIds,
      resolveNote: resolveNote
    }
  })
}

// 获取错误日志统计信息
export function getErrorLogStats(query) {
  return request({
    url: '/log/error/stats',
    method: 'get',
    params: query
  })
}

// 获取错误日志趋势数据
export function getErrorLogTrend(query) {
  return request({
    url: '/log/error/trend',
    method: 'get',
    params: query
  })
}

// 获取错误分类统计
export function getErrorCategoryStats(query) {
  return request({
    url: '/log/error/categoryStats',
    method: 'get',
    params: query
  })
}
