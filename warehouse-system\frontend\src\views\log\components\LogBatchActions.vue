<template>
  <el-row :gutter="10" class="mb8">
    <!-- 主要操作按钮 -->
    <el-col :span="1.5" v-for="(action, index) in mainActions" :key="'main-' + index">
      <el-button
        :type="action.type"
        plain
        :icon="action.icon"
        size="mini"
        :disabled="action.disabled"
        @click="handleAction(action.key)"
        :v-hasPermi="action.permission ? [action.permission] : undefined"
      >{{ action.label }}</el-button>
    </el-col>
    
    <!-- 批量操作按钮 - 如果只有一个操作，直接显示按钮；多个操作才用下拉菜单 -->
    <template v-if="batchActions && batchActions.length">
      <!-- 单个批量操作：直接显示按钮 -->
      <el-col :span="1.5" v-if="batchActions.length === 1">
        <el-button
          type="danger"
          plain
          :icon="batchActions[0].icon"
          size="mini"
          @click="handleBatchAction(batchActions[0].key)"
          :v-hasPermi="batchActions[0].permission ? [batchActions[0].permission] : undefined"
        >{{ batchActions[0].label }}</el-button>
      </el-col>
      
      <!-- 多个批量操作：使用下拉菜单 -->
      <el-col :span="1.5" v-else>
        <el-dropdown @command="handleBatchAction" size="mini">
          <el-button type="info" plain size="mini">
            批量操作<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item 
              v-for="(action, index) in batchActions" 
              :key="'batch-' + index"
              :command="action.key" 
              :icon="action.icon"
            >{{ action.label }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-col>
    </template>
    
    <!-- 额外操作按钮 -->
    <el-col :span="1.5" v-for="(action, index) in extraActions" :key="'extra-' + index">
      <el-button
        :type="action.type"
        plain
        :icon="action.icon"
        size="mini"
        @click="handleAction(action.key)"
      >{{ action.label }}</el-button>
    </el-col>
    
    <!-- 右侧工具栏 -->
    <right-toolbar :showSearch.sync="showSearchSync" @queryTable="refreshTable" :search="true"></right-toolbar>
  </el-row>
</template>

<script>
export default {
  name: "LogBatchActions",
  props: {
    mainActions: {
      type: Array,
      default: () => []
    },
    batchActions: {
      type: Array,
      default: () => []
    },
    extraActions: {
      type: Array,
      default: () => []
    },
    showSearch: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    showSearchSync: {
      get() {
        return this.showSearch
      },
      set(value) {
        this.$emit('update:showSearch', value)
      }
    }
  },
  methods: {
    handleAction(actionKey) {
      this.$emit('action', actionKey);
    },
    handleBatchAction(actionKey) {
      this.$emit('batch-action', actionKey);
    },
    refreshTable() {
      this.$emit('refresh');
    }
  }
};
</script>

<style scoped>
.mb8 {
  margin-bottom: 8px;
}
</style>