<template>
  <div class="error-boundary-wrapper">
    <!-- 正常内容 -->
    <div v-if="!hasError" class="content-wrapper">
      <slot></slot>
    </div>
    
    <!-- 错误状态 -->
    <div v-else class="error-boundary">
      <div class="error-icon">
        <i class="el-icon-warning-outline"></i>
      </div>
      <div class="error-title">{{ errorTitle }}</div>
      <div class="error-message">{{ errorMessage }}</div>
      <div class="error-details" v-if="showDetails && errorDetails">
        <el-collapse>
          <el-collapse-item title="错误详情" name="details">
            <pre>{{ errorDetails }}</pre>
          </el-collapse-item>
        </el-collapse>
      </div>
      <div class="error-actions">
        <el-button type="primary" @click="handleRetry">
          <i class="el-icon-refresh"></i>
          重试
        </el-button>
        <el-button @click="handleReload">
          <i class="el-icon-refresh-right"></i>
          刷新页面
        </el-button>
        <el-button v-if="showReport" @click="handleReport">
          <i class="el-icon-message"></i>
          报告问题
        </el-button>
      </div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="retrying" class="retry-loading">
      <el-loading-spinner></el-loading-spinner>
      <span>正在重试...</span>
    </div>
  </div>
</template>

<script>
import { errorHandler } from '@/utils/performance'

export default {
  name: 'ErrorBoundary',
  props: {
    // 自定义错误标题
    errorTitle: {
      type: String,
      default: '页面出现异常'
    },
    // 自定义错误消息
    errorMessage: {
      type: String,
      default: '抱歉，页面遇到了一些问题。您可以尝试刷新页面或稍后再试。'
    },
    // 是否显示错误详情
    showDetails: {
      type: Boolean,
      default: false
    },
    // 是否显示报告按钮
    showReport: {
      type: Boolean,
      default: true
    },
    // 最大重试次数
    maxRetries: {
      type: Number,
      default: 3
    },
    // 重试延迟（毫秒）
    retryDelay: {
      type: Number,
      default: 1000
    },
    // 是否自动重试
    autoRetry: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      hasError: false,
      errorDetails: '',
      retrying: false,
      retryCount: 0,
      errorInfo: null
    }
  },
  
  errorCaptured(err, instance, info) {
    console.error('ErrorBoundary captured error:', err, info)
    
    this.hasError = true
    this.errorDetails = `${err.message}\n\n${err.stack}`
    this.errorInfo = {
      error: err,
      instance: instance,
      info: info,
      timestamp: Date.now(),
      component: instance ? instance.$options.name : 'Unknown',
      route: this.$route ? this.$route.path : 'Unknown'
    }
    
    // 记录错误到性能监控系统
    errorHandler.recordError({
      type: 'component',
      message: err.message,
      stack: err.stack,
      component: this.errorInfo.component,
      route: this.errorInfo.route,
      info: info,
      timestamp: this.errorInfo.timestamp
    })
    
    // 触发错误事件
    this.$emit('error', this.errorInfo)
    
    // 自动重试
    if (this.autoRetry && this.retryCount < this.maxRetries) {
      this.scheduleRetry()
    }
    
    // 阻止错误继续向上传播
    return false
  },
  
  methods: {
    // 重试操作
    async handleRetry() {
      if (this.retryCount >= this.maxRetries) {
        this.$message.warning(`已达到最大重试次数（${this.maxRetries}次）`)
        return
      }
      
      this.retrying = true
      this.retryCount++
      
      try {
        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.retryDelay))
        
        // 重置错误状态
        this.hasError = false
        this.errorDetails = ''
        this.errorInfo = null
        
        // 触发重试事件
        this.$emit('retry', this.retryCount)
        
        // 强制重新渲染子组件
        this.$nextTick(() => {
          this.$forceUpdate()
        })
        
        this.$message.success('重试成功')
        
      } catch (error) {
        console.error('重试失败:', error)
        this.$message.error('重试失败，请稍后再试')
      } finally {
        this.retrying = false
      }
    },
    
    // 刷新页面
    handleReload() {
      this.$confirm('确定要刷新页面吗？未保存的数据将会丢失。', '确认刷新', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        window.location.reload()
      }).catch(() => {
        // 用户取消
      })
    },
    
    // 报告问题
    handleReport() {
      const reportData = {
        error: this.errorInfo,
        userAgent: navigator.userAgent,
        timestamp: Date.now(),
        url: window.location.href,
        userId: this.$store ? this.$store.getters.userId : 'unknown'
      }
      
      // 这里可以发送错误报告到服务器
      console.log('错误报告数据:', reportData)
      
      this.$message.success('问题已报告，我们会尽快处理')
      this.$emit('report', reportData)
    },
    
    // 计划重试
    scheduleRetry() {
      setTimeout(() => {
        this.handleRetry()
      }, this.retryDelay * (this.retryCount + 1)) // 递增延迟
    },
    
    // 重置错误状态
    reset() {
      this.hasError = false
      this.errorDetails = ''
      this.errorInfo = null
      this.retryCount = 0
      this.retrying = false
    }
  },
  
  watch: {
    // 监听路由变化，重置错误状态
    '$route'() {
      if (this.hasError) {
        this.reset()
      }
    }
  },
  
  // 组件销毁时清理
  beforeDestroy() {
    this.reset()
  }
}
</script>

<style scoped>
.error-boundary-wrapper {
  position: relative;
  min-height: 200px;
}

.content-wrapper {
  width: 100%;
  height: 100%;
}

.error-boundary {
  padding: 40px;
  text-align: center;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 8px;
  margin: 20px 0;
}

.error-icon {
  font-size: 48px;
  color: #f56c6c;
  margin-bottom: 16px;
}

.error-title {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
  margin-bottom: 8px;
}

.error-message {
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.error-details {
  margin: 20px 0;
  text-align: left;
}

.error-details pre {
  background: #f5f5f5;
  padding: 15px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.error-actions {
  margin-top: 20px;
}

.error-actions .el-button {
  margin: 0 8px;
}

.retry-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.retry-loading span {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-boundary {
    padding: 20px;
    margin: 10px 0;
  }
  
  .error-icon {
    font-size: 36px;
  }
  
  .error-title {
    font-size: 16px;
  }
  
  .error-actions .el-button {
    display: block;
    width: 100%;
    margin: 8px 0;
  }
}
</style>
