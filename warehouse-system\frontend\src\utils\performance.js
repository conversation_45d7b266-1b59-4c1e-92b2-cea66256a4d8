/**
 * 性能优化工具类
 * 提供页面加载性能优化、用户体验提升和友好错误处理功能
 */

// 防抖函数
export function debounce(func, wait, immediate) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      timeout = null;
      if (!immediate) func(...args);
    };
    const callNow = immediate && !timeout;
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
    if (callNow) func(...args);
  };
}

// 节流函数
export function throttle(func, limit) {
  let inThrottle;
  return function(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

// 懒加载工具
export class LazyLoader {
  constructor(options = {}) {
    this.options = {
      threshold: 0.1,
      rootMargin: '50px',
      ...options
    };
    this.observer = null;
    this.init();
  }

  init() {
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        this.options
      );
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const target = entry.target;
        const callback = target.dataset.lazyCallback;
        if (callback && window[callback]) {
          window[callback](target);
        }
        this.observer.unobserve(target);
      }
    });
  }

  observe(element) {
    if (this.observer) {
      this.observer.observe(element);
    }
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect();
    }
  }
}

// 缓存管理器
export class CacheManager {
  constructor(maxSize = 100) {
    this.cache = new Map();
    this.maxSize = maxSize;
  }

  get(key) {
    if (this.cache.has(key)) {
      // 更新访问时间
      const value = this.cache.get(key);
      this.cache.delete(key);
      this.cache.set(key, value);
      return value;
    }
    return null;
  }

  set(key, value, ttl = 300000) { // 默认5分钟过期
    if (this.cache.size >= this.maxSize) {
      // 删除最旧的项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    const item = {
      value,
      timestamp: Date.now(),
      ttl
    };
    
    this.cache.set(key, item);
  }

  has(key) {
    if (this.cache.has(key)) {
      const item = this.cache.get(key);
      if (Date.now() - item.timestamp > item.ttl) {
        this.cache.delete(key);
        return false;
      }
      return true;
    }
    return false;
  }

  clear() {
    this.cache.clear();
  }

  size() {
    return this.cache.size;
  }
}

// 性能监控器
export class PerformanceMonitor {
  constructor() {
    this.metrics = {
      pageLoadTime: 0,
      apiResponseTimes: [],
      errorCount: 0,
      userActions: []
    };
    this.init();
  }

  init() {
    // 监控页面加载时间
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.metrics.pageLoadTime = loadTime;
      console.log(`页面加载时间: ${loadTime.toFixed(2)}ms`);
    });

    // 监控API响应时间
    this.interceptFetch();
  }

  interceptFetch() {
    const originalFetch = window.fetch;
    window.fetch = (...args) => {
      const startTime = performance.now();
      return originalFetch(...args).then(response => {
        const endTime = performance.now();
        const responseTime = endTime - startTime;
        this.metrics.apiResponseTimes.push({
          url: args[0],
          time: responseTime,
          timestamp: Date.now()
        });
        
        if (responseTime > 3000) {
          console.warn(`API响应时间过长: ${args[0]} - ${responseTime.toFixed(2)}ms`);
        }
        
        return response;
      }).catch(error => {
        this.metrics.errorCount++;
        throw error;
      });
    };
  }

  recordUserAction(action, details = {}) {
    this.metrics.userActions.push({
      action,
      details,
      timestamp: Date.now()
    });
  }

  getMetrics() {
    return {
      ...this.metrics,
      averageApiResponseTime: this.getAverageApiResponseTime(),
      slowApiCalls: this.getSlowApiCalls()
    };
  }

  getAverageApiResponseTime() {
    if (this.metrics.apiResponseTimes.length === 0) return 0;
    const total = this.metrics.apiResponseTimes.reduce((sum, item) => sum + item.time, 0);
    return total / this.metrics.apiResponseTimes.length;
  }

  getSlowApiCalls() {
    return this.metrics.apiResponseTimes.filter(item => item.time > 2000);
  }
}

// 错误处理器
export class ErrorHandler {
  constructor() {
    this.errorQueue = [];
    this.maxErrors = 50;
    this.init();
  }

  init() {
    // 全局错误监听
    window.addEventListener('error', this.handleError.bind(this));
    window.addEventListener('unhandledrejection', this.handlePromiseRejection.bind(this));
  }

  handleError(event) {
    const error = {
      type: 'javascript',
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      stack: event.error ? event.error.stack : '',
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.recordError(error);
    this.showUserFriendlyError('页面出现异常，请刷新页面重试');
  }

  handlePromiseRejection(event) {
    const error = {
      type: 'promise',
      message: event.reason ? event.reason.message : '未知Promise错误',
      stack: event.reason ? event.reason.stack : '',
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    this.recordError(error);
    this.showUserFriendlyError('操作失败，请稍后重试');
  }

  recordError(error) {
    if (this.errorQueue.length >= this.maxErrors) {
      this.errorQueue.shift(); // 移除最旧的错误
    }
    this.errorQueue.push(error);
    
    // 发送错误到服务器（可选）
    this.reportError(error);
  }

  reportError(error) {
    // 这里可以发送错误到服务器进行统计分析
    console.error('错误记录:', error);
  }

  showUserFriendlyError(message) {
    // 显示用户友好的错误提示
    if (window.Vue && window.Vue.prototype.$message) {
      window.Vue.prototype.$message.error(message);
    } else {
      alert(message);
    }
  }

  getErrors() {
    return this.errorQueue;
  }

  clearErrors() {
    this.errorQueue = [];
  }
}

// 加载状态管理器
export class LoadingManager {
  constructor() {
    this.loadingStates = new Map();
    this.globalLoading = false;
  }

  setLoading(key, loading = true) {
    this.loadingStates.set(key, loading);
    this.updateGlobalLoading();
  }

  isLoading(key) {
    return this.loadingStates.get(key) || false;
  }

  updateGlobalLoading() {
    const hasLoading = Array.from(this.loadingStates.values()).some(loading => loading);
    this.globalLoading = hasLoading;
    
    // 更新全局加载状态
    document.body.classList.toggle('global-loading', hasLoading);
  }

  isGlobalLoading() {
    return this.globalLoading;
  }

  clearAll() {
    this.loadingStates.clear();
    this.globalLoading = false;
    document.body.classList.remove('global-loading');
  }
}

// 创建全局实例
export const lazyLoader = new LazyLoader();
export const cacheManager = new CacheManager();
export const performanceMonitor = new PerformanceMonitor();
export const errorHandler = new ErrorHandler();
export const loadingManager = new LoadingManager();

// 工具函数
export const utils = {
  // 格式化文件大小
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 格式化时间
  formatTime(ms) {
    if (ms < 1000) return `${ms.toFixed(0)}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}min`;
  },

  // 检查网络状态
  getNetworkStatus() {
    return {
      online: navigator.onLine,
      connection: navigator.connection || navigator.mozConnection || navigator.webkitConnection,
      effectiveType: navigator.connection ? navigator.connection.effectiveType : 'unknown'
    };
  }
};

// Vue混入 - 性能优化
export const performanceMixin = {
  data() {
    return {
      loading: false,
      searchLoading: false,
      exportLoading: false
    };
  },

  methods: {
    // 防抖搜索
    debouncedSearch: debounce(function() {
      this.handleQuery();
    }, 500),

    // 节流刷新
    throttledRefresh: throttle(function() {
      this.handleRefresh();
    }, 1000),

    // 安全的API调用
    async safeApiCall(apiFunction, loadingKey = 'loading', errorMessage = '操作失败，请稍后重试') {
      try {
        this[loadingKey] = true;
        loadingManager.setLoading(loadingKey, true);

        const result = await apiFunction();
        return result;
      } catch (error) {
        console.error('API调用失败:', error);
        this.$message.error(errorMessage);
        errorHandler.recordError({
          type: 'api',
          message: error.message,
          stack: error.stack,
          timestamp: Date.now(),
          component: this.$options.name
        });
        throw error;
      } finally {
        this[loadingKey] = false;
        loadingManager.setLoading(loadingKey, false);
      }
    },

    // 缓存API调用
    async cachedApiCall(cacheKey, apiFunction, ttl = 300000) {
      if (cacheManager.has(cacheKey)) {
        const cached = cacheManager.get(cacheKey);
        return cached.value;
      }

      const result = await apiFunction();
      cacheManager.set(cacheKey, result, ttl);
      return result;
    },

    // 记录用户操作
    recordUserAction(action, details = {}) {
      performanceMonitor.recordUserAction(action, {
        ...details,
        component: this.$options.name,
        route: this.$route.path
      });
    }
  },

  created() {
    // 记录组件创建时间
    this._componentCreateTime = performance.now();
  },

  mounted() {
    // 记录组件挂载时间
    const mountTime = performance.now() - this._componentCreateTime;
    console.log(`组件 ${this.$options.name} 挂载时间: ${mountTime.toFixed(2)}ms`);

    // 记录用户访问
    this.recordUserAction('component_visit', {
      componentName: this.$options.name,
      mountTime: mountTime
    });
  }
};

export default {
  debounce,
  throttle,
  LazyLoader,
  CacheManager,
  PerformanceMonitor,
  ErrorHandler,
  LoadingManager,
  lazyLoader,
  cacheManager,
  performanceMonitor,
  errorHandler,
  loadingManager,
  utils,
  performanceMixin
};
